from django.db import models
from core.models import UserProfile, PartnerProfile
import uuid
from django.core.exceptions import ValidationError
import enum
from .templates.templates import (checkin_initiated_template, checkout_template, order_accepted_template,
                                  order_cancelled_template, order_completed_template, partner_order_placed_template,
                                  pre_checkin_confirmed_template, pre_checkin_created_template
                                )
from core.models import User
class NotificationChannel(enum.Enum):
    MESSAGE = 'MESSAGE'
    EMAIL = 'EMAIL'
    WHATSAPP = 'WHATSAPP'
    PUSH = 'PUSH'

NotificationChannelHandler = {
    'PUSH': 'notification.channel.firebase.FirebaseChannel',
    'MESSAGE': 'notification.channel.message.MessageChannel',
}

class NotificationCategory(enum.Enum):
    USER_CHECKIN_INITIATED = 'USER_CHECKIN_INITIATED'
    USER_CHECKIN = 'USER_CHECKIN'
    USER_CHECKOUT = 'USER_CHECKOUT'
    USER_ORDER_ACCEPTED = 'USER_ORDER_ACCEPTED'
    USER_ORDER_CANCELLED = 'USER_ORDER_CANCELLED'
    USER_ORDER_COMPLETED = 'USER_ORDER_COMPLETED'
    PARTNER_ORDER_PLACED = 'PARTNER_ORDER_PLACED'  
    PRE_CHECKIN_CREATED = 'PRE_CHECKIN_CREATED'
    PRECHECKIN_CONFIRMED = 'PRECHECKIN_CONFIRMED'

    @staticmethod
    def __from__name__(name):
        for category in NotificationCategory:
            if category.name == name:
                return category
        return None


NotificationTemplates = {
    'USER_CHECKIN_INITIATED': (checkin_initiated_template, {'username':'', 'room_no':'', 'property_name':''}),
    'USER_CHECKOUT': (checkout_template, {'username':'', 'property_name':''}),
    'USER_ORDER_ACCEPTED': (order_accepted_template, {'username':'','order_id':''}),
    'USER_ORDER_CANCELLED': (order_cancelled_template, {'username':'','order_id':''}),
    'USER_ORDER_COMPLETED': (order_completed_template, {'username':'','order_id':''}),
    'PARTNER_ORDER_PLACED': (partner_order_placed_template, {'partner_name':'', 'order_id':'', 'room_no':'', 'guest_name':''}),
    'PRE_CHECKIN_CREATED': (pre_checkin_created_template, {'guest_name':'', 'property_owner_name':'', 'expected_date':'', 'room_number':''}),
    'PRECHECKIN_CONFIRMED': (pre_checkin_confirmed_template, {'guest_name':'', 'property_owner_name':'', 'expected_date':'', 'room_number':''}),
}
                                                        

class UserNotificationProfile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name='notification_profile')
    
    def clean(self):
        if self.cleaned_data['user'].is_partner:
            raise ValidationError('User is a partner, create a partner notification profile instead')
        
    def __str__(self) -> str:
        return self.user.user.name

class PartnerNotificationProfile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    partner = models.OneToOneField(PartnerProfile, on_delete=models.CASCADE, related_name='notification_profile')

    def clean(self):
        if not self.cleaned_data['partner'].user.is_partner:
            raise ValidationError('User is not a partner, create a user notification profile instead')
        
    def __str__(self) -> str:
        return self.partner.user.name

class Notification(models.Model):
    class UserTypeOptions(models.IntegerChoices):
        USER = 1
        PARTNER = 2

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.CharField(max_length=50, choices=[(tag.name, tag.value) for tag in NotificationCategory])
    channel = models.CharField(max_length=50, choices=[(tag.name, tag.value) for tag in NotificationChannel])
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    user_type = models.PositiveSmallIntegerField(choices=UserTypeOptions.choices)

    def __str__(self):
        return self.category + ' - ' + self.channel
    
class NotificationSubscription(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, blank=True, null=True)
    partner = models.ForeignKey(PartnerProfile, on_delete=models.CASCADE, blank=True, null=True)
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name='subscriptions')
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.user.user.name + ' - ' + self.notification.category + ' - ' + self.notification.channel
    
class NotificationLog(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_notification_profile = models.ForeignKey(UserNotificationProfile, on_delete=models.CASCADE, blank=True, null=True)
    partner_notification_profile = models.ForeignKey(PartnerNotificationProfile, on_delete=models.CASCADE, blank=True, null=True)
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE)
    is_sent = models.BooleanField(default=False)
    is_read = models.BooleanField(default=False)
    error_msg = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.notification.category + ' - ' + self.notification.channel
    

class FirebaseDeviceToken(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='firebase_device_tokens')
    token = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.name + ' - ' + self.token