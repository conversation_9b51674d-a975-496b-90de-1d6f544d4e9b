import os
from pathlib import Path
from dotenv import load_dotenv
import os
import dj_database_url
from datetime import timedelta
from firebase_admin import initialize_app


load_dotenv()
MODE = os.getenv("DJANGO_ENV", 'local')
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-u*73boz$y)mm10cokvyzzw70#67^i@5i4^ek57!$5te@her8w7"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["0.0.0.0", "*"]
CSRF_TRUSTED_ORIGINS = ['https://*', 'https://guestdev.nestafar.com', 'https://partnerdev.nestafar.com']

CORS_ALLOWED_ORIGINS = [
    "https://guestdev.nestafar.com",
    "https://partnerdev.nestafar.com",
    "https://*.nestafar.com"
]

#CORS_ORIGIN_ALLOW_ALL = True
#CORS_ALLOW_CREDENTIALS = False


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "phonenumber_field",
    "celery",
    "django_celery_beat",
    "django_celery_results",
    "django_extensions",
    # "fcm_django",
    "corsheaders",
    "service",
    "stay",
    "core",
    "service.subapps.food",
    "service.subapps.shop",
    "service.subapps.tourism",
    "service.subapps.transport",
    "service.subapps.rental",
    "service.subapps.laundry",
    "rest_framework_simplejwt",
    'rest_framework_simplejwt.token_blacklist',
    "geo",
    "notification",
    "booking"
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "core.middlewares.jwt.JWTAuthenticationMiddleware",
    "core.middlewares.property.PropertyMiddleware",
    "nestafar.logger.DjangoRequestLogger",
]
if MODE != "dev":
    MIDDLEWARE.append("whitenoise.middleware.WhiteNoiseMiddleware")

ROOT_URLCONF = "nestafar.urls"

# Authentication settings

AUTHENTICATION_BACKENDS = [
    "core.auth.OtpAuthBackend",
]
LOGIN_URL = "/core/login/"

AUTH_USER_MODEL = "core.User"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "nestafar.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

if MODE == "test":
    DATABASES = {
        "default": {
            "ENGINE": "django_cockroachdb",
            "NAME": "backenddb",
            "HOST": "crdb",
            "PORT": "26257",
        },
    }
if MODE == "prod":
    DATABASES = {
        "default": dj_database_url.config(
            default=os.getenv("PROD_DATABASE_URL"), engine="django_cockroachdb"
        )
    }

if MODE == "dev":
    DATABASES = {'default': dj_database_url.config(default=os.getenv('DEV_DATABASE_URL'), engine='django_cockroachdb')}

if MODE == "local":
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }

if MODE != "dev":
    STORAGES = {
        "default": {
            "BACKEND": "storages.backends.s3.S3Storage",
        },
        "staticfiles": {
            "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
        },
    }
else:
    STORAGES = {
        "default": {
            "BACKEND": "storages.backends.s3.S3Storage",
        },
        "staticfiles": {
            "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
        },
    }
    # DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

# Comment this when not using local DB

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "en-us"

CELERY_TIMEZONE='Asia/Kolkata'
TIME_ZONE = 'Asia/Kolkata'
CELERY_ENABLE_UTC = True

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/
# if MODE != 'dev' :
#     STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

STATIC_URL = "/static/"
STATIC_ROOT = "static"

# STATICFILES_DIRS = (os.path.join(BASE_DIR, "static"),)
# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.AutoField"


# AWS S3
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME")
AWS_S3_SIGNATURE_NAME = ("s3v4",)
AWS_S3_REGION_NAME = os.getenv("AWS_S3_REGION_NAME")
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = None
AWS_S3_VERITY = True
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024 #10mb file limit

# Phone number field settings
PHONENUMBER_DEFAULT_REGION = "IN"
PHONENUMBER_DB_FORMAT = "E164"
PHONENUMBER_DEFAULT_FORMAT = "E164"

# CELERY SETTINGS
REDIS_HOST = "redis"
if MODE == "dev" or MODE == "local":
    REDIS_HOST = "localhost"
if MODE == "prod":
    REDIS_HOST = os.getenv("REDIS_HOST")
if MODE=="test":
    REDIS_HOST = "redis"

CELERY_BROKER_URL = "redis://" + REDIS_HOST
result_backend = "redis://" + REDIS_HOST

# CELERY BEAT SCHEDULER

CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True


# REDIS CACHE

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://" + REDIS_HOST,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

# Google Maps settings
GOOGLE_MAPS_API_KEY = os.getenv("GOOGLE_MAPS_API_KEY")

# Fast 2 SMS Settings
FAST2SMS_API_KEY = os.getenv(
    "FAST2SMS_API_KEY"
)
FAST2SMS_API_ENDPOINT = "https://www.fast2sms.com/dev/bulkV2"

# Rest Framework settings : https://www.django-rest-framework.org/api-guide/settings/

REST_FRAMEWORK = {
    # Use Django's standard `django.contrib.auth` permissions,
    # or allow read-only access for unauthenticated users.
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly"
    ],
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
}

# JWT Settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=7),  # 1 week
    "REFRESH_TOKEN_LIFETIME": timedelta(days=14),  # 2 week
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
}
# LOGGING_FILES_DIR= os.path.join(BASE_DIR, "logs")
LOGGING_FILES_DIR = "logs"

try: 
    from .local_settings import *
except ImportError:
    pass

CDN_HOST = os.getenv('CDN_DOMAIN')

if CDN_HOST:
    AWS_S3_CUSTOM_DOMAIN = CDN_HOST



# Optional ONLY IF you have initialized a firebase app already:
# Visit https://firebase.google.com/docs/admin/setup/#python
# for more options for the following:
# Store an environment variable called GOOGLE_APPLICATION_CREDENTIALS
# which is a path that point to a json file with your credentials.
# Additional arguments are available: credentials, options, name
FIREBASE_APP = initialize_app()
# To learn more, visit the docs here:
# https://cloud.google.com/docs/authentication/getting-started>

FCM_DJANGO_SETTINGS = {
     # an instance of firebase_admin.App to be used as default for all fcm-django requests
     # default: None (the default Firebase app)
    "DEFAULT_FIREBASE_APP": "Nestafar",
     # default: _('FCM Django')
    "APP_VERBOSE_NAME": "_(FCM Nestafar)",
     # true if you want to have only one active device per registered user at a time
     # default: False
    "ONE_DEVICE_PER_USER": True,
     # devices to which notifications cannot be sent,
     # are deleted upon receiving error response from FCM
     # default: False
    "DELETE_INACTIVE_DEVICES": True,
}
