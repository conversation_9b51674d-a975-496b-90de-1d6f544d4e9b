from django.dispatch import receiver
from django.db.models.signals import post_save
from core.models import UserProfile, PartnerProfile
from .models import UserNotificationProfile, PartnerNotificationProfile
from .tasks import subscribe_user_to_notifications

@receiver(post_save, sender=UserProfile)
def create_user_notification_profile(sender, instance, created, **kwargs):
    if created:
        UserNotificationProfile.objects.create(user=instance)
        subscribe_user_to_notifications(instance)

@receiver(post_save, sender=PartnerProfile)
def create_partner_notification_profile(sender, instance, created, **kwargs):
    if created:
        PartnerNotificationProfile.objects.create(partner=instance)
        subscribe_user_to_notifications(instance)