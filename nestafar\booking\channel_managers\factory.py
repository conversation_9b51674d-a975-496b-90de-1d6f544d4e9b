"""
Channel Manager Factory for creating appropriate adapter instances.
"""

import logging
from typing import Dict, Type, Optional
from django.conf import settings

from .base import BaseChannelManager
from .aiosell import AIOSellChannelManager
from .booking_com import BookingComChannelManager

logger = logging.getLogger(__name__)


class ChannelManagerRegistry:
    """
    Registry for managing available channel manager adapters.
    """
    
    def __init__(self):
        self._adapters: Dict[str, Type[BaseChannelManager]] = {}
        self._register_default_adapters()
    
    def _register_default_adapters(self):
        """Register the default channel manager adapters."""
        self.register('aiosell', AIOSellChannelManager)
        self.register('booking_com', BookingComChannelManager)  # Example adapter
    
    def register(self, name: str, adapter_class: Type[BaseChannelManager]):
        """
        Register a new channel manager adapter.
        
        Args:
            name: Unique name for the channel manager
            adapter_class: Class that implements BaseChannelManager
        """
        if not issubclass(adapter_class, BaseChannelManager):
            raise ValueError(f"Adapter class must inherit from BaseChannelManager")
        
        self._adapters[name.lower()] = adapter_class
        logger.info(f"Registered channel manager adapter: {name}")
    
    def get_adapter_class(self, name: str) -> Optional[Type[BaseChannelManager]]:
        """
        Get the adapter class for a given channel manager name.
        
        Args:
            name: Name of the channel manager
            
        Returns:
            Adapter class or None if not found
        """
        return self._adapters.get(name.lower())
    
    def list_adapters(self) -> Dict[str, Type[BaseChannelManager]]:
        """
        Get all registered adapters.
        
        Returns:
            Dictionary of adapter name to adapter class mappings
        """
        return self._adapters.copy()


# Global registry instance
_registry = ChannelManagerRegistry()


class ChannelManagerFactory:
    """
    Factory for creating channel manager adapter instances.
    """
    
    @staticmethod
    def create_adapter(channel_name: str) -> Optional[BaseChannelManager]:
        """
        Create a channel manager adapter instance.
        
        Args:
            channel_name: Name of the channel manager (e.g., 'aiosell', 'booking_com')
            
        Returns:
            Channel manager adapter instance or None if not found
        """
        adapter_class = _registry.get_adapter_class(channel_name)
        
        if adapter_class is None:
            logger.error(f"Unknown channel manager: {channel_name}")
            return None
        
        try:
            return adapter_class()
        except Exception as e:
            logger.error(f"Failed to create adapter for {channel_name}: {str(e)}")
            return None
    
    @staticmethod
    def get_adapter_for_property(property_obj) -> Optional[BaseChannelManager]:
        """
        Get the appropriate channel manager adapter for a property.

        Args:
            property_obj: Property model instance

        Returns:
            Channel manager adapter instance or None
        """
        # First, try to use the primary channel manager if set
        if hasattr(property_obj, 'primary_channel_manager') and property_obj.primary_channel_manager:
            if property_obj.is_channel_manager_enabled(property_obj.primary_channel_manager):
                return ChannelManagerFactory.create_adapter(property_obj.primary_channel_manager)

        # Fallback: check for any enabled channel managers
        if hasattr(property_obj, 'get_enabled_channel_managers'):
            enabled_managers = property_obj.get_enabled_channel_managers()
            if enabled_managers:
                return ChannelManagerFactory.create_adapter(enabled_managers[0])

        # Legacy fallback: if property has hotel_code, assume AIOSell
        if hasattr(property_obj, 'hotel_code') and property_obj.hotel_code:
            return ChannelManagerFactory.create_adapter('aiosell')

        return None
    
    @staticmethod
    def get_adapter_by_webhook_path(webhook_path: str) -> Optional[BaseChannelManager]:
        """
        Get the appropriate channel manager adapter based on webhook URL path.
        
        Args:
            webhook_path: The webhook URL path (e.g., '/webhook/aiosell/')
            
        Returns:
            Channel manager adapter instance or None
        """
        if not webhook_path or not isinstance(webhook_path, str):
            logger.warning(f"Invalid webhook path: {webhook_path}")
            return None

        # Extract channel name from webhook path
        # Expected format: /webhook/{channel_name}/
        path_parts = webhook_path.strip('/').split('/')

        if len(path_parts) >= 2 and path_parts[0] == 'webhook':
            channel_name = path_parts[1].strip()
            if not channel_name:
                logger.warning(f"Empty channel name in webhook path: {webhook_path}")
                return None
            return ChannelManagerFactory.create_adapter(channel_name)

        logger.debug(f"Webhook path does not match expected format: {webhook_path}")
        return None
    
    @staticmethod
    def register_adapter(name: str, adapter_class: Type[BaseChannelManager]):
        """
        Register a new channel manager adapter.
        
        Args:
            name: Unique name for the channel manager
            adapter_class: Class that implements BaseChannelManager
        """
        _registry.register(name, adapter_class)
    
    @staticmethod
    def list_available_adapters() -> Dict[str, Type[BaseChannelManager]]:
        """
        Get all available channel manager adapters.
        
        Returns:
            Dictionary of adapter name to adapter class mappings
        """
        return _registry.list_adapters()


# Convenience functions for common operations
def get_channel_manager(channel_name: str) -> Optional[BaseChannelManager]:
    """
    Convenience function to get a channel manager adapter.
    
    Args:
        channel_name: Name of the channel manager
        
    Returns:
        Channel manager adapter instance or None
    """
    return ChannelManagerFactory.create_adapter(channel_name)


def register_channel_manager(name: str, adapter_class: Type[BaseChannelManager]):
    """
    Convenience function to register a new channel manager adapter.
    
    Args:
        name: Unique name for the channel manager
        adapter_class: Class that implements BaseChannelManager
    """
    ChannelManagerFactory.register_adapter(name, adapter_class)
