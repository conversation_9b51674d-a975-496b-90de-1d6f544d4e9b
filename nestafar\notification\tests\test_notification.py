from nestafar.core.tests.test_user import TestUser
from core.models import User
from notification.models import Notification, NotificationCategory, NotificationChannel, NotificationLog, FirebaseDeviceToken
from notification.tasks import send_notification

class TestNotification(TestUser):
    partner_phone = '9876543210'
    partner_password = 'password'
    guest_phone = '9876543211'
    guest_password = 'password'

    def setUp(self):
        Notification.objects.create(
            category=NotificationCategory.USER_CHECKIN_INITIATED.name,
            channel=NotificationChannel.MESSAGE.name,
            description="User checkin initiated",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_CHECKIN.name,
            channel=NotificationChannel.PUSH.name,
            description="User checkin",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_CHECKOUT.name,
            channel=NotificationChannel.PUSH.name,
            description="User checkout",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_ORDER_ACCEPTED.name,
            channel=NotificationChannel.PUSH.name,
            description="User order accepted",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_ORDER_CANCELLED.name,
            channel=NotificationChannel.PUSH.name,
            description="User order cancelled",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_ORDER_COMPLETED.name,
            channel=NotificationChannel.PUSH.name,
            description="User order completed",
            user_type=Notification.UserTypeOptions.USER
        )

        Notification.objects.create(
            category=NotificationCategory.PARTNER_ORDER_PLACED.name,
            channel=NotificationChannel.PUSH.name,
            description="Partner order placed",
            user_type=Notification.UserTypeOptions.PARTNER
        )

        partner=User.objects.create_user(
            phone=self.partner_phone,
            name="Partner",
            password=self.partner_password,
            partner=True
        )
        guest=User.objects.create_user(
            phone=self.guest_phone,
            name="Guest",
            password=self.guest_password,
            partner=False
        )
        FirebaseDeviceToken.objects.create(
            user=partner,
            token='token_partner'
        )
        FirebaseDeviceToken.objects.create(
            user=guest,
            token='token_guest'
        )
    
    def test_subscribe_user_to_notifications(self):
        partner = User.objects.get(phone=self.partner_phone)
        guest = User.objects.get(phone=self.guest_phone)
        self.assertTrue(partner.is_partner)
        self.assertFalse(guest.is_partner)
        notifications = Notification.objects.filter(
            user_type=Notification.UserTypeOptions.PARTNER
        )
        self.assertNotEqual(len(notifications), 0)
        for notification in notifications:
            self.assertEqual(
                notification.subscriptions.filter(
                    partner=partner.partner_profile
                ).count(),
                1
            )
        notifications = Notification.objects.filter(
            user_type=Notification.UserTypeOptions.USER
        )
        self.assertNotEqual(len(notifications), 0)
        for notification in notifications:
            self.assertEqual(
                notification.subscriptions.filter(
                    user=guest.user_profile
                ).count(),
                1
            )
    
    def test_notification(self):
        partner = User.objects.get(phone=self.partner_phone)
        guest = User.objects.get(phone=self.guest_phone)

        success,_ = send_notification(
            partner.id,
            NotificationCategory.PARTNER_ORDER_PLACED.name,
            {
                'partner_name': 'Partner',
                'order_id': '123',
                'room_no': '101',
                'guest_name': 'Guest'
            }
        )
        self.assertTrue(success)
        notification=Notification.objects.filter(
            category=NotificationCategory.PARTNER_ORDER_PLACED.name
        ).first()
        log=NotificationLog.objects.filter(
            partner_notification_profile__partner__user=partner,
            notification=notification
        ).first()
        self.assertNotEqual(log, None)
        success,_ = send_notification(
            guest.id,
            NotificationCategory.USER_ORDER_COMPLETED.name,
            {
                'order_id': '123',
                'username': 'Guest'
            }
        )
        self.assertTrue(success)
        notification = Notification.objects.filter(
            category=NotificationCategory.USER_ORDER_COMPLETED.name
        ).first()
        log=NotificationLog.objects.filter(
            user_notification_profile__user__user=guest,
            notification=notification
        ).first()
        self.assertNotEqual(log, None)
        



