from django.dispatch import receiver
from django.db.models.signals import post_save
from .subapps.food.models import FoodOrder
from .subapps.laundry.models import LaundryOrder
from .subapps.rental.models import RentalOrder
from .subapps.transport.models import TransportOrder
from .subapps.shop.models import ShopOrder
from .subapps.tourism.models import TourismOrder

# List of order models to listen to
ORDER_MODELS = [FoodOrder, LaundryOrder, RentalOrder, TransportOrder, ShopOrder, TourismOrder]


def update_cart_status_on_rejected(instance):
    """Reset the cart if all orders in it are rejected."""
    instance.remove_from_cart()
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list('status', flat=True)
    if all(order == instance.OrderStatus.REJECTED for order in orders):
        instance.cart.reset_cart()
        instance.cart.status = instance.cart.CartStatus.REJECTED
        instance.cart.save()


def update_cart_status_on_accepted(instance):
    """Update cart status to ACCEPTED if all orders are accepted, otherwise PARTIALLY_ACCEPTED."""
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list('status', flat=True)
    if all(order == instance.OrderStatus.ACCEPTED for order in orders):
        instance.cart.status = instance.cart.CartStatus.ACCEPTED
    else:
        instance.cart.status = instance.cart.CartStatus.PARTIALLY_ACCEPTED
    instance.cart.save()


def update_cart_status_on_completed(instance):
    """Update cart status to COMPLETED if all orders are completed, rejected, or cancelled."""
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list('status', flat=True)
    if all(order in {instance.OrderStatus.COMPLETED, instance.OrderStatus.REJECTED, instance.OrderStatus.CANCELLED} 
           for order in orders):
        instance.cart.status = instance.cart.CartStatus.COMPLETED
        instance.cart.save()


def update_cart_status_on_cancelled(instance):
    """Update cart status to CANCELLED if all orders are cancelled."""
    instance.remove_from_cart()
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list('status', flat=True)
    if all(order == instance.OrderStatus.CANCELLED for order in orders):
        instance.cart.status = instance.cart.CartStatus.CANCELLED
        instance.cart.save()


@receiver(post_save, sender=FoodOrder)
@receiver(post_save, sender=LaundryOrder)
@receiver(post_save, sender=RentalOrder)
@receiver(post_save, sender=TransportOrder)
@receiver(post_save, sender=ShopOrder)
@receiver(post_save, sender=TourismOrder)
def update_cart_status(sender, instance, **kwargs):
    """Signal to update the cart status based on the order's status."""
    if instance.status == instance.OrderStatus.PENDING:
        instance.cart.status = instance.cart.CartStatus.ORDERED
        instance.cart.save()

    elif instance.status == instance.OrderStatus.ACCEPTED:
        update_cart_status_on_accepted(instance)

    elif instance.status == instance.OrderStatus.REJECTED:
        update_cart_status_on_rejected(instance)

    elif instance.status == instance.OrderStatus.COMPLETED:
        update_cart_status_on_completed(instance)

    elif instance.status == instance.OrderStatus.CANCELLED:
        update_cart_status_on_cancelled(instance)
