from .models import Notification, NotificationSubscription, NotificationCategory, NotificationTemplates, NotificationLog, NotificationChannelHandler
from core.models import UserProfile, PartnerProfile, User
from celery import shared_task
import importlib



def subscribe_user_to_notifications(profile):
    if isinstance(profile, PartnerProfile):
        notifications=Notification.objects.filter(
            user_type=Notification.UserTypeOptions.PARTNER
        )
        for notification in notifications:
            NotificationSubscription.objects.create(
                partner=profile,
                notification=notification
            )
    else:
        notifications=Notification.objects.filter(
            user_type=Notification.UserTypeOptions.USER
        )
        for notification in notifications:
            NotificationSubscription.objects.create(
                user=profile,
                notification=notification
            )

def validate_data_for_notification(category, data):
    notification_template=NotificationTemplates.get(category.name)
    if not notification_template:
        return False, 'No template found for ' + category.name
    template, datav = notification_template
    for key in datav:
        if key not in data:
            return False, 'Missing ' + key + ' in data'
    return True, template(**data)

def load_handler_from_channel_name(channel_name:str):
    handler_string = NotificationChannelHandler.get(channel_name)
    mods = handler_string.split('.')
    module = importlib.import_module('.'.join(mods[:-1]))
    handler = getattr(module, mods[-1])
    return handler


@shared_task
def send_notification(user_id:str, event:str, data:dict):
    category=NotificationCategory.__from__name__(event)
    if not category:
        raise ValueError('Invalid event name ' + event)
    error_msg=None
    
    notification_types=Notification.objects.filter(
        category=category.name
    )
    validated, template = validate_data_for_notification(category, data)
    if not validated:
        error_msg="Invalid data for notification"
        return False, error_msg
    title, body = template
    user = User.objects.get(id=user_id)
    if user.is_partner:
        notification_profile=user.partner_profile.notification_profile
    else:
        notification_profile=user.user_profile.notification_profile
    for notification in notification_types:
        handler=load_handler_from_channel_name(notification.channel)
        if handler is None:
            continue
        try:
            success,msg = handler().send_message(user.id, title, body)
            if not success:
                error_msg=msg
        except Exception as e:
            error_msg=str(e)            
        notification_log=NotificationLog.objects.create(
            notification=notification,
        )
        if user.is_partner:
            notification_log.partner_notification_profile=notification_profile
        else:
            notification_log.user_notification_profile=notification_profile
        notification_log.error_msg=error_msg
        notification_log.save()
    return True, 'Notification in Queue'