from django.contrib import admin
from django.utils.html import format_html
from .models import (
    CheckinRequest, Guest, Property, PropertyMetaData,
    PropertyReview, PropertyPartner, Room, RoomPhotos
)


@admin.register(CheckinRequest)
class CheckinRequestAdmin(admin.ModelAdmin):
    list_display = ('user', 'property', 'room_no', 'status', 'created_at')
    search_fields = ('user__name', 'property__name', 'room_no')
    list_filter = ('status', 'created_at')
    ordering = ('-created_at',)
    readonly_fields = ('id',)


@admin.register(Guest)
class GuestAdmin(admin.ModelAdmin):
    list_display = (
        'user', 'room', 'checkin_key', 'checked_in', 'checked_out', 
        'total_orders', 'total_spends', 'check_in_date', 'check_out_date'
    )
    search_fields = ('user__name', 'room__room_no', 'room__property__name')
    list_filter = ('checked_in', 'checked_out', 'room__property__name')
    ordering = ('-created_at',)
    readonly_fields = ('id', 'created_at', 'updated_at')


@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    list_display = ('name', 'location', 'type', 'rating', 'rooms', 'avg_price')
    search_fields = ('name', 'location__name', 'po_address')
    list_filter = ('type', 'rating', 'location__administrative_area__name')
    readonly_fields = ('id',)
    fieldsets = (
        ('Basic Info', {
            'fields': ('id', 'name', 'location', 'description', 'po_address', 'photo', 'hotel_code')
        }),
        ('Details', {
            'fields': ('type', 'rooms', 'rating', 'avg_price', 'meal_cost', 'directions', 'amenities', 'staffs')
        }),
    )


@admin.register(PropertyMetaData)
class PropertyMetaDataAdmin(admin.ModelAdmin):
    list_display = ('property', 'key', 'value')
    search_fields = ('property__name', 'key')
    list_filter = ('property__name',)
    ordering = ('property', 'key')


@admin.register(PropertyReview)
class PropertyReviewAdmin(admin.ModelAdmin):
    list_display = ('property', 'user', 'rating', 'review')
    search_fields = ('property__name', 'user__name', 'review')
    list_filter = ('rating',)
    ordering = ('property',)


@admin.register(PropertyPartner)
class PropertyPartnerAdmin(admin.ModelAdmin):
    list_display = ('property', 'partner', 'commission', 'in_house')
    search_fields = ('property__name', 'partner__name', 'name')
    list_filter = ('property__name', 'in_house')


@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = ('room_no', 'property', 'type_of_room', 'rate', 'max_guests', 'occupied', 'checked_in')
    search_fields = ('property__name', 'room_no', 'type_of_room')
    list_filter = ('property__name', 'occupied', 'checked_in', 'max_guests')
    ordering = ('property', 'room_no')


@admin.register(RoomPhotos)
class RoomPhotosAdmin(admin.ModelAdmin):
    list_display = ('room', 'description', 'primary')
    search_fields = ('room__property__name', 'room__room_no', 'description')
    list_filter = ('primary', 'room__property__name')
