"""
AIOSell Channel Manager adapter for handling reservations.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Tuple, Optional
from django.db import transaction
from django.core.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework import status

from .base import BaseChannelManager
from booking.models import Reservation
from stay.models import Property
from core.utils import get_or_create_user

logger = logging.getLogger(__name__)


class AIOSellChannelManager(BaseChannelManager):
    """
    AIOSell-specific implementation of the Channel Manager interface.
    
    This adapter handles the AIOSell webhook data format and implements
    all reservation operations according to AIOSell's specifications.
    """
    
    def __init__(self):
        super().__init__('aiosell')
    
    def validate_webhook_data(self, data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate AIOSell webhook data format.
        
        Args:
            data: Raw webhook data dictionary
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Valid action types
        VALID_ACTIONS = {'book', 'modify', 'cancel', 'inventory'}
        
        # Check required base fields
        required_fields = ['action', 'hotelCode', 'bookingId']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            return False, f'Missing required fields: {", ".join(missing_fields)}'
        
        # Validate action-specific fields
        action = data.get('action', '').lower()
        
        if action not in VALID_ACTIONS:
            return False, f'Invalid action: {action}. Must be one of: {", ".join(VALID_ACTIONS)}'
        
        if action == 'book':
            booking_required_fields = [
                'checkin', 'checkout', 'guest', 'rooms', 'amount'
            ]
            missing_booking_fields = [
                field for field in booking_required_fields 
                if field not in data
            ]
            if missing_booking_fields:
                return False, f'Missing required fields for booking: {", ".join(missing_booking_fields)}'
        
        return True, None
    
    def parse_guest_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse guest information from AIOSell webhook data.
        
        Args:
            data: Raw webhook data dictionary
            
        Returns:
            Standardized guest data dictionary
        """
        guest_data = data.get('guest', {})
        address_data = guest_data.get('address', {})
        
        return {
            'first_name': guest_data.get('firstName', ''),
            'last_name': guest_data.get('lastName', ''),
            'email': guest_data.get('email', ''),
            'phone': guest_data.get('phone', ''),
            'address': {
                'line1': address_data.get('line1', ''),
                'city': address_data.get('city', ''),
                'state': address_data.get('state', ''),
                'country': address_data.get('country', ''),
                'zip_code': address_data.get('zipCode', '')
            }
        }
    
    def parse_room_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse room details from AIOSell webhook data.
        
        Args:
            data: Raw webhook data dictionary
            
        Returns:
            Standardized room data dictionary
        """
        rooms_data = data.get('rooms', [])
        total_guests = 0
        
        for room in rooms_data:
            occupancy = room.get('occupancy', {})
            try:
                adults = int(occupancy.get('adults', 0))
                children = int(occupancy.get('children', 0))
                total_guests += max(0, adults) + max(0, children)
            except (ValueError, TypeError):
                logger.warning(f"Invalid occupancy data in room: {room}")
        
        return {
            'rooms_data': rooms_data,
            'total_guests': total_guests
        }
    
    def create_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Create a new reservation from AIOSell webhook data.
        
        Args:
            data: Validated webhook data dictionary
            
        Returns:
            Django REST framework Response object
        """
        try:
            with transaction.atomic():
                # Get property by hotel code
                hotel_code = data.get('hotelCode')
                try:
                    property_obj = Property.objects.get(hotel_code=hotel_code)
                except Property.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'Property not found for hotel code: {hotel_code}'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # Atomic check for duplicate booking with row-level lock
                external_booking_id = data.get('bookingId')
                
                # Use select_for_update to prevent race conditions
                existing_reservation = Reservation.objects.select_for_update().filter(
                    external_booking_id=external_booking_id
                ).first()
                
                if existing_reservation:
                    return Response({
                        'success': False,
                        'message': f'Reservation already exists for booking ID: {external_booking_id}'
                    }, status=status.HTTP_409_CONFLICT)
                
                # Parse dates
                try:
                    check_in = datetime.strptime(data.get('checkin'), '%Y-%m-%d')
                    check_out = datetime.strptime(data.get('checkout'), '%Y-%m-%d')
                except ValueError:
                    return Response({
                        'success': False,
                        'message': 'Invalid date format. Use YYYY-MM-DD'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Parse booked_on date
                booked_on = None
                if data.get('bookedOn'):
                    try:
                        booked_on = datetime.strptime(data.get('bookedOn'), '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        logger.warning(f"Invalid bookedOn date format: {data.get('bookedOn')}")
                
                # Parse guest data
                guest_info = self.parse_guest_data(data)
                user = get_or_create_user(
                    phone=guest_info['phone'],
                    email=guest_info['email'],
                    first_name=guest_info['first_name'],
                    last_name=guest_info['last_name']
                )
                
                # Parse room data
                room_info = self.parse_room_data(data)
                
                # Parse amount data
                amount_data = data.get('amount', {})
                total_amount = amount_data.get('amountAfterTax', 0)
                amount_before_tax = amount_data.get('amountBeforeTax', 0)
                tax_amount = amount_data.get('tax', 0)
                currency = amount_data.get('currency', 'INR')
                # Validate currency code
                VALID_CURRENCIES = {'INR', 'USD', 'EUR', 'GBP'}  # Add more as needed
                if currency not in VALID_CURRENCIES:
                    logger.warning(f"Invalid currency code: {currency}, defaulting to INR")
                    currency = 'INR'

                # Determine payment status
                pah = data.get('pah', False)
                paid_amount = 0 if pah else total_amount
                # Create the reservation
                reservation = Reservation.objects.create(
                    user=user,
                    property=property_obj,
                    check_in=check_in,
                    check_out=check_out,
                    guests=room_info['total_guests'],
                    total=total_amount,
                    paid=paid_amount,
                    requests=data.get('specialRequests', ''),
                    status='confirmed',
                    booking_details=data,
                    
                    # AIOSell specific fields
                    external_booking_id=external_booking_id,
                    cm_booking_id=data.get('cmBookingId'),
                    channel=data.get('channel'),
                    segment=data.get('segment', 'OTA'),
                    pah=pah,
                    booked_on=booked_on,
                    amount_before_tax=amount_before_tax,
                    tax_amount=tax_amount,
                    currency=currency,
                    room_details=room_info['rooms_data']
                )
                
                logger.info(f"AIOSell reservation created: {reservation.id} for booking {external_booking_id}")
                
                return Response({
                    'success': True,
                    'message': 'Reservation created successfully',
                    'reservation_id': str(reservation.id),
                    'booking_id': external_booking_id
                }, status=status.HTTP_201_CREATED)
                
        except Exception as e:
            logger.error(f"AIOSell booking creation error: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': 'Failed to create reservation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def modify_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Modify an existing reservation from AIOSell webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        try:
            with transaction.atomic():
                external_booking_id = data.get('bookingId')

                # Find existing reservation
                try:
                    reservation = Reservation.objects.get(external_booking_id=external_booking_id)
                except Reservation.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'Reservation not found for booking ID: {external_booking_id}'
                    }, status=status.HTTP_404_NOT_FOUND)

                # Parse updated dates if provided
                if data.get('checkin'):
                    try:
                        reservation.check_in = datetime.strptime(data.get('checkin'), '%Y-%m-%d')
                    except ValueError:
                        return Response({
                            'success': False,
                            'message': 'Invalid checkin date format. Use YYYY-MM-DD'
                        }, status=status.HTTP_400_BAD_REQUEST)

                if data.get('checkout'):
                    try:
                        reservation.check_out = datetime.strptime(data.get('checkout'), '%Y-%m-%d')
                    except ValueError:
                        return Response({
                            'success': False,
                            'message': 'Invalid checkout date format. Use YYYY-MM-DD'
                        }, status=status.HTTP_400_BAD_REQUEST)

                # Validate date consistency after any date updates
                if reservation.check_out <= reservation.check_in:
                    return Response({
                        'success': False,
                        'message': 'Check-out date must be after check-in date'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Update amount if provided
                if data.get('amount'):
                    amount_data = data.get('amount')
                    reservation.total = amount_data.get('amountAfterTax', reservation.total)
                    reservation.amount_before_tax = amount_data.get('amountBeforeTax', reservation.amount_before_tax)
                    reservation.tax_amount = amount_data.get('tax', reservation.tax_amount)
                    reservation.currency = amount_data.get('currency', reservation.currency)

                # Update special requests if provided
                if data.get('specialRequests'):
                    reservation.requests = data.get('specialRequests')

                # Update room details and guest count if provided
                if data.get('rooms'):
                    room_info = self.parse_room_data(data)
                    if room_info['total_guests'] > 0:
                        reservation.guests = room_info['total_guests']
                    reservation.room_details = room_info['rooms_data']

                # Update PAH status
                if 'pah' in data:
                    reservation.pah = data.get('pah')
                    if not reservation.pah and reservation.paid == 0:
                        reservation.paid = reservation.total  # Mark as prepaid

                # Update booking details with new data
                if not reservation.booking_details:
                    reservation.booking_details = {}
                reservation.booking_details.update(data)

                reservation.save()

                logger.info(f"AIOSell reservation modified: {reservation.id} for booking {external_booking_id}")

                return Response({
                    'success': True,
                    'message': 'Reservation modified successfully',
                    'reservation_id': str(reservation.id),
                    'booking_id': external_booking_id
                }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"AIOSell booking modification error: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': 'Failed to modify reservation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def cancel_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Cancel an existing reservation from AIOSell webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        try:
            with transaction.atomic():
                external_booking_id = data.get('bookingId')

                # Find existing reservation
                try:
                    reservation = Reservation.objects.get(external_booking_id=external_booking_id)
                except Reservation.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'Reservation not found for booking ID: {external_booking_id}'
                    }, status=status.HTTP_404_NOT_FOUND)

                # Check if reservation can be cancelled
                if reservation.status == 'cancelled':
                    return Response({
                        'success': True,
                        'message': 'Reservation already cancelled'
                    }, status=status.HTTP_200_OK)

                if reservation.status == 'checked_out':
                    return Response({
                        'success': False,
                        'message': 'Cannot cancel a checked-out reservation'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Update reservation status
                reservation.status = 'cancelled'

                # Update booking details with cancellation data
                if reservation.booking_details:
                    reservation.booking_details.update(data)
                else:
                    reservation.booking_details = data

                reservation.save()

                logger.info(f"AIOSell reservation cancelled: {reservation.id} for booking {external_booking_id}")

                return Response({
                    'success': True,
                    'message': 'Reservation cancelled successfully',
                    'reservation_id': str(reservation.id),
                    'booking_id': external_booking_id
                }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"AIOSell booking cancellation error: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': 'Failed to cancel reservation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update_inventory(self, data: Dict[str, Any]) -> Response:
        """
        Update room inventory from AIOSell webhook data.

        Note: This is a placeholder implementation as inventory management
        is not currently implemented in the system. This method can be
        extended when inventory management features are added.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        logger.info(f"AIOSell inventory update received: {data}")

        return Response({
            'success': True,
            'message': 'Inventory update received (not implemented yet)'
        }, status=status.HTTP_200_OK)
