from django.contrib import admin
from django.utils.html import format_html
from .models import ServicePartner
from django.urls import reverse

@admin.register(ServicePartner)
class ServicePartnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'type_of_service', 'phone_number', 'location', 'service_count', 'view_services_link', 'is_visible']
    search_fields = ['name', 'phone_number', 'is_visible']
    list_filter = ['type_of_service', 'location']
    ordering = ['name']   
    inlines = [] 
    readonly_fields = ['service_count', 'view_services_link']

    # Fieldset organization to make editing more intuitive
    fieldsets = (
        (None, {
            'fields': ('name', 'location', 'type_of_service', 'description', 'phone_number', 'is_visible')
        }),
        ('Services', {
            'fields': ('service_count', 'view_services_link'),
            'classes': ('collapse',),  # Collapse section for better UI
        }),
    )
    
    # Custom property to display count of services
    def service_count(self, obj):
        return obj.services().count()

    service_count.short_description = 'Number of Services'

    # Add a link to view services in the list display
    def view_services_link(self, obj):
        if obj.service:
            # Get the service's admin URL for the specific service type
            service_model_name = obj.service._meta.model_name
            app_label = obj.service._meta.app_label
            # Generate the correct link with the partner's UUID in the query string
            link = reverse(f'admin:{app_label}_{service_model_name}_changelist')
            return format_html('<a href="{}?partner={}">View Services</a>', link, obj.id)
        return '-'

    view_services_link.short_description = 'Services Link'

