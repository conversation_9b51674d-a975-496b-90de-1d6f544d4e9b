from stay.models import Room, Property, Guest, CheckinRequest
from rest_framework import serializers
from core.models import User
from phonenumber_field.serializerfields import PhoneNumberField
from geo.models import State, City, AdministrationArea
from service.subapps.food.models import FoodService


class UserSerializer(serializers.ModelSerializer):
    phone = PhoneNumberField()
    image = serializers.SerializerMethodField(required=False)

    def get_image(self, obj):
        return obj.user_profile.image.url if obj.user_profile.image else None

    class Meta:
        model = User
        exclude = ["password"]


class PropertySerializer(serializers.ModelSerializer):
    class Meta:
        model = Property
        fields = ["id", "name", "avg_price", "rooms", "location", "hotel_code"]


class PropertyListSerializer(PropertySerializer):
    class Meta:
        model = Property
        fields = ["id", "name", "avg_price", "rooms", "photo", "location", "amenities", "hotel_code"]


class PropertyAddressSerializer(serializers.Serializer):
    address = serializers.CharField()
    pincode = serializers.CharField()
    state = serializers.CharField()
    city = serializers.CharField()

    def validate(self, attrs):
        if not City.objects.filter(name=attrs["city"]).exists():
            raise serializers.ValidationError("City not found")
        if not State.objects.filter(name=attrs["state"]).exists():
            raise serializers.ValidationError("State not found")
        if not AdministrationArea.objects.filter(pincode=attrs["pincode"]).exists():
            raise serializers.ValidationError("Pincode not found")
        return attrs


class PropertyRetrieveSerializer(serializers.ModelSerializer):
    location = serializers.SerializerMethodField()

    def get_location(self, obj):
        from geo.serializers import LocationSerializer
        return LocationSerializer(obj.location).data

    class Meta:
        model = Property
        fields = ["id", "name", "avg_price", "rooms", "photo", "location", "amenities"]


class RoomCreateSerializer(serializers.ModelSerializer):
    def validate(self, attrs):
        ctx_property = self.context.get("property")
        if ctx_property.property_rooms.filter(room_no=attrs.get('room_no')).exists():
            raise serializers.ValidationError("Duplicate room number not allowed")
        return attrs

    class Meta:
        model = Room
        fields = ["type_of_room", "rate", "description", "bed", "max_guests", "room_no"]

class RoomUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Room
        fields = ["type_of_room", "rate", "description", "bed", "max_guests", "room_no"]

class RoomSerializer(serializers.ModelSerializer):
    photo = serializers.SerializerMethodField()
    first_guest = serializers.SerializerMethodField()
    property = serializers.SerializerMethodField()

    def get_first_guest(self, obj):
        checked_in_guests = obj.guest.filter(checked_in=True, checked_out=False)
        if checked_in_guests.exists():
            return checked_in_guests.last().user.name
        return None

    def get_photo(self, obj):
        photo = obj.photos.filter(primary=True).last()
        if not photo:
            photo = obj.photos.last()
        return photo.image.url if photo else None
    
    def get_property(self, obj):
        return obj.property.name

    class Meta:
        model = Room
        fields = ["id", "type_of_room", "rate", "property", "description", "running_total",
                  "bed", "max_guests", "room_no", "photo", "amenities", "first_guest"]


class RoomRetrieveSerializer(serializers.ModelSerializer):
    photos = serializers.SerializerMethodField()

    def get_photos(self, obj):
        return [photo.image.url for photo in obj.photos.all()]

    class Meta:
        model = Room
        fields = ["id", "type_of_room", "rate", "property", "description",
                  "bed", "max_guests", "room_no", "photos", "amenities", "checked_in"]


class CheckInGuestSerializer(serializers.Serializer):
    name = serializers.CharField(required=True)
    phone = serializers.CharField(required=True)


class CheckInSerializer(serializers.Serializer):
    room_id = serializers.UUIDField(required=True)
    guests = serializers.ListField(child=CheckInGuestSerializer(), required=True)
    group_id = serializers.CharField(required=False)

    def validate(self, attrs):
        r = Room.objects.filter(id=attrs["room_id"])
        if not r.exists():
            raise serializers.ValidationError("Room not found")
        if r.get().max_guests < len(attrs['guests']):
            raise serializers.ValidationError("Room can accommodate up to " + str(r.get().max_guests) + " guests")
        return attrs


class GuestCheckInSerializer(serializers.Serializer):
    qr_key = serializers.CharField(required=True)
    consent = serializers.BooleanField(required=True)

    def validate(self, attrs):
        user = self.context.get("user")
        if not attrs["consent"]:
            raise serializers.ValidationError("You need to give consent")
        if user.is_partner:
            raise serializers.ValidationError("You are not a guest")
        if not Guest.objects.filter(checkin_key=attrs["qr_key"], user__name=user.name).exists():
            raise serializers.ValidationError("No checkin initiated")
        return attrs


class CompleteCheckinSerializer(serializers.Serializer):
    qr_key = serializers.CharField(required=True)

    def validate(self, attrs):
        user = self.context.get("user")
        guest = Guest.objects.filter(checkin_key=attrs["qr_key"])
        if not guest.exists():
            raise serializers.ValidationError("No checkin found")
        if not set(guest.values_list("checked_in", flat=True)) == {True}:
            raise serializers.ValidationError("Guest is not checked in")
        if not user.is_partner:
            raise serializers.ValidationError("You are not a partner")
        return attrs


class GuestSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    room = RoomSerializer()

    class Meta:
        model = Guest
        fields = ["id", "room", "checkin_key", "user", "checked_in", "checked_out", "check_in_date", "check_out_date"]


class PropertyDataVerificationSerializer(serializers.ModelSerializer):
    partners = serializers.SerializerMethodField()
    service_partners = serializers.SerializerMethodField()
    staffs = serializers.SerializerMethodField()
    services = serializers.SerializerMethodField()
    checked_in_guests = serializers.SerializerMethodField()

    def get_service_partners(self, obj):
        return obj.property_partner.all().values_list("partner__name")

    def get_partners(self, obj):
        return obj.property_partner.all().values_list("name")

    def get_staffs(self, obj):
        return obj.staffs.all().values_list("user__name")

    def get_services(self, obj):
        service_partners = obj.property_partner.all().values_list('partner', flat=True)
        fs = FoodService.objects.filter(partner__in=service_partners)
        return fs.values_list("name")

    def get_checked_in_guests(self, obj):
        guests = Guest.objects.filter(room__property=obj, checked_in=True)
        return guests.values_list("user__name")

    class Meta:
        model = Property
        fields = ["name", "avg_price", "rooms", "location", "amenities",
                  "partners", "service_partners", "staffs", "services", "checked_in_guests"]

class CheckinRequestSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    property = PropertySerializer()

    class Meta:
        model = CheckinRequest
        fields = ["id", "user", "property", "room_no", "created_at", "completed"]

