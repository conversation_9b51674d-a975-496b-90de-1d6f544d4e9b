from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from booking.views import (
    ReservationViewSet, ProfileViewSet,
    GuestPreCheckinViewSet, PreCheckinViewSet
)
from booking.webhook_views import aiosell_webhook, channel_manager_webhook

# Create a router and register the ReservationViewSet with it
router = DefaultRouter()
router.register(r'reservations', ReservationViewSet, basename='reservation')
router.register(r'profiles', ProfileViewSet, basename='profile')
router.register(r'precheckin', PreCheckinViewSet, basename='precheckin')

urlpatterns = [
    path('', include(router.urls)),
    # Guest pre-checkin endpoints
    path('guest/precheckin/setup/<uuid:pk>/', GuestPreCheckinViewSet.as_view({'patch': 'setup'}), name='setup'),
    path('guest/precheckin/cancel/<uuid:pk>/', GuestPreCheckinViewSet.as_view({'patch': 'cancel'}), name='cancel'),
    path('guest/precheckin/upload-id/', GuestPreCheckinViewSet.as_view({'post': 'upload_id'}), name='guest-upload-id'),
    path('guest/precheckin/', GuestPreCheckinViewSet.as_view({'get': 'retrieve'}), name='guest-precheckin'),
    
    # Channel Manager webhook endpoints
    path('webhook/<str:channel_name>/', channel_manager_webhook, name='channel-manager-webhook'),

    # Legacy AIOSell webhook endpoint (for backward compatibility)
    path('webhook/aiosell/', aiosell_webhook, name='aiosell-webhook'),
]
