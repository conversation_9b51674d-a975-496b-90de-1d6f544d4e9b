from rest_framework import serializers
from booking.models import PreCheckinGuest, Reservation, Profile, ProfileImage, PreCheckin, AllotedRoom, Payment
from django.utils import timezone
from stay.serializers import RoomSerializer, PropertySerializer

class ReservationSerializer(serializers.ModelSerializer):
    property = PropertySerializer(read_only=True)
    
    class Meta:
        model = Reservation
        fields = [
            'id', 'property', 'check_in', 'check_out', 'guests', 'total', 
            'paid', 'requests', 'status', 'created_at', 'updated_at',
            'booking_details', 'external_booking_id', 'cm_booking_id',
            'channel', 'segment', 'hotel_code', 'pah', 'booked_on',
            'amount_before_tax', 'tax_amount', 'currency', 'room_details'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'external_booking_id', 
            'cm_booking_id', 'booked_on', 'amount_before_tax', 
            'tax_amount', 'currency'
        ]

    def validate(self, data):
        """Ensure check-out date is after check-in date."""
        if data['check_out'] <= data['check_in']:
            raise serializers.ValidationError("Check-out must be after check-in.")
        if data['guests'] <= 0:
            raise serializers.ValidationError("There must be at least one guest.")
        return data

class ProfileImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProfileImage
        fields = ['id', 'image', 'created_at', 'updated_at']

class ProfileSerializer(serializers.ModelSerializer):
    images = ProfileImageSerializer(many=True, required=False)
    property = PropertySerializer(read_only=True)

    class Meta:
        model = Profile
        fields = [
            'id', 'property', 'phone', 'email', 'description',
            'location', 'amenities', 'nearby', 'images', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        images_data = self.context['request'].FILES.getlist('images')
        profile = Profile.objects.create(**validated_data)
        
        for image_data in images_data:
            ProfileImage.objects.create(profile=profile, image=image_data)
            
        return profile

    def update(self, instance, validated_data):
        images_data = self.context['request'].FILES.getlist('images')
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # If images were provided, replace the existing images with new ones
        if images_data:
            instance.images.all().delete()  # Delete existing images
            for image_data in images_data:
                ProfileImage.objects.create(profile=instance, image=image_data)
        
        return instance

class UserSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    phone = serializers.CharField()  # Changed from PhoneNumberField to CharField

class PreCheckinGuestSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    room = RoomSerializer(read_only=True)
    
    class Meta:
        model = PreCheckinGuest
        fields = [
            'id', 'pre_checkin', 'user', 'room', 'id_proof', 
            'is_verified', 'is_primary', 'age', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'is_verified']

class AllotedRoomSerializer(serializers.ModelSerializer):
    room = RoomSerializer()
    
    class Meta:
        model = AllotedRoom
        fields = ['id', 'pre_checkin', 'room', 'created_at']
        read_only_fields = ['id', 'created_at']

class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = [
            'id', 'pre_checkin', 'amount', 'payment_method', 
            'transaction_id', 'status', 'payment_details', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

class PreCheckinSerializer(serializers.ModelSerializer):
    guests = PreCheckinGuestSerializer(many=True, read_only=True, source='pre_checkin_guests')
    alloted_rooms = AllotedRoomSerializer(many=True, read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)
    property = PropertySerializer(read_only=True)
    
    class Meta:
        model = PreCheckin
        fields = [
            'id', 'property', 'guest_address', 'number_of_rooms',
            'expected_checkin', 'stay_duration', 'welcome_message', 
            'total_amount', 'amount_paid', 'pending_balance',
            'payment_status', 'payment_id', 'status',
            'special_requests', 'created_at', 'guests',
            'alloted_rooms', 'payments'
        ]
        read_only_fields = [
            'id', 'created_at', 'status', 'property', 
            'guests', 'alloted_rooms', 'payments', 
        ]
    
    def validate_expected_checkin(self, value):
        """Validate expected checkin date."""
        if value < timezone.now():
            raise serializers.ValidationError(
                "Expected checkin date cannot be in the past"
            )
        return value

    def validate_stay_duration(self, value):
        """Validate stay duration."""
        if value <= 0:
            raise serializers.ValidationError(
                "Stay duration must be greater than 0"
            )
        return value

    def validate_number_of_rooms(self, value):
        """Validate number of rooms."""
        if value <= 0:
            raise serializers.ValidationError(
                "Number of rooms must be greater than 0"
            )
        return value

    def validate(self, data):
        """Validate the entire data set."""
        # Calculate pending balance
        total_amount = data.get('total_amount', 0)
        amount_paid = data.get('amount_paid', 0)
        
        pending_balance = float(total_amount) - float(amount_paid)
        if pending_balance < 0:
            raise serializers.ValidationError({
                "amount_paid": "Amount paid cannot exceed total amount"
            })

        # Auto-set payment status based on amounts
        if amount_paid == 0:
            data['payment_status'] = 'unpaid'
        elif amount_paid == total_amount:
            data['payment_status'] = 'completed'
        else:
            data['payment_status'] = 'partial'

        data['pending_balance'] = pending_balance

        return data

    def to_representation(self, instance):
        """Customize the output representation."""
        representation = super().to_representation(instance)
        
        # Add primary guest details if exists
        primary_guest = instance.pre_checkin_guests.filter(is_primary=True).first()
        if primary_guest:
            representation['primary_guest'] = {
                'id': str(primary_guest.id),
                'name': primary_guest.user.name,
                'phone': str(primary_guest.user.phone),
                'age': primary_guest.age,
                'is_verified': primary_guest.is_verified
            }
        
        return representation


