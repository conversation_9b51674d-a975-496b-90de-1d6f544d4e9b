"""
AIOSell Webhook Views for handling reservation updates from OTAs
"""
import logging
from datetime import datetime
from django.db import transaction
from django.core.exceptions import ValidationError
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import json
from booking.models import Reservation
from stay.models import Property
from core.utils import get_or_create_user

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([AllowAny])
def aiosell_webhook(request):
    """
    Webhook endpoint for AIOSell to send reservation updates.
    Handles booking creation, modification, and cancellation.
    """
    try:
        # Parse the incoming JSON data
        if not request.body:
            return Response({
                'success': False,
                'message': 'Empty request body'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return Response({
                'success': False,
                'message': 'Invalid JSON format'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate required fields
        required_fields = ['action', 'hotelCode', 'bookingId']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            return Response({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        action = data.get('action')
        
        # Route to appropriate handler based on action
        if action == 'book':
            return handle_booking_creation(data)
        elif action == 'modify':
            return handle_booking_modification(data)
        elif action == 'cancel':
            return handle_booking_cancellation(data)
        else:
            return Response({
                'success': False,
                'message': f'Invalid action: {action}. Valid actions are: book, modify, cancel'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"AIOSell webhook error: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_booking_creation(data):
    """Handle new booking creation from AIOSell"""
    try:
        with transaction.atomic():
            # Validate required fields for booking creation
            required_fields = [
                'hotelCode', 'bookingId', 'checkin', 'checkout', 
                'guest', 'rooms', 'amount'
            ]
            
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return Response({
                    'success': False,
                    'message': f'Missing required fields for booking: {", ".join(missing_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if booking already exists
            external_booking_id = data.get('bookingId')
            if Reservation.objects.filter(external_booking_id=external_booking_id).exists():
                return Response({
                    'success': False,
                    'message': f'Booking with ID {external_booking_id} already exists'
                }, status=status.HTTP_409_CONFLICT)

            # Find the property by hotel code
            property_obj = get_property_by_hotel_code(data.get('hotelCode'))
            if not property_obj:
                return Response({
                    'success': False,
                    'message': f'Property not found for hotel code: {data.get("hotelCode")}'
                }, status=status.HTTP_404_NOT_FOUND)

            # Parse guest information
            guest_data = data.get('guest', {})
            guest_name = f"{guest_data.get('firstName', '')} {guest_data.get('lastName', '')}".strip()
            guest_phone = guest_data.get('phone', '')
            guest_email = guest_data.get('email', '')

            if not guest_name or not guest_phone:
                return Response({
                    'success': False,
                    'message': 'Guest name and phone are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create or get user
            try:
                user = get_or_create_user(guest_name, guest_phone, guest_email)
            except ValidationError as e:
                return Response({
                    'success': False,
                    'message': f'Invalid guest data: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Parse dates
            try:
                check_in = datetime.strptime(data.get('checkin'), '%Y-%m-%d')
                check_out = datetime.strptime(data.get('checkout'), '%Y-%m-%d')
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Parse booked_on date if provided
            booked_on = None
            if data.get('bookedOn'):
                try:
                    booked_on = datetime.strptime(data.get('bookedOn'), '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    try:
                        booked_on = datetime.strptime(data.get('bookedOn'), '%Y-%m-%d')
                    except ValueError:
                        logger.warning(f"Invalid bookedOn format: {data.get('bookedOn')}")

            # Parse amount information
            amount_data = data.get('amount', {})
            try:
                total_amount = float(amount_data.get('amountAfterTax', 0))
            except (ValueError, TypeError):
                return Response({
                    'success': False,
                    'message': 'Invalid amount format'
                }, status=status.HTTP_400_BAD_REQUEST)
            amount_before_tax = amount_data.get('amountBeforeTax')
            tax_amount = amount_data.get('tax')
            currency = amount_data.get('currency') or 'INR'  # Default to INR if currency is not provided or empty

            # Calculate total guests from rooms
            total_guests = 0
            rooms_data = data.get('rooms', [])
            for room in rooms_data:
                occupancy = room.get('occupancy', {})
                try:
                    adults = max(0, int(occupancy.get('adults', 0)))
                except (ValueError, TypeError):
                    adults = 0
                    logger.warning(f"Invalid adults value in occupancy: {occupancy.get('adults')}")
                
                try:
                    children = max(0, int(occupancy.get('children', 0)))
                except (ValueError, TypeError):
                    children = 0
                    logger.warning(f"Invalid children value in occupancy: {occupancy.get('children')}")
                
                total_guests += adults + children

            if total_guests == 0:
                total_guests = 1  # Default to 1 guest if not specified

            # Determine payment status
            pah = data.get('pah', False)
            paid_amount = 0 if pah else total_amount

            # Create the reservation
            reservation = Reservation.objects.create(
                user=user,
                property=property_obj,
                check_in=check_in,
                check_out=check_out,
                guests=total_guests,
                total=total_amount,
                paid=paid_amount,
                requests=data.get('specialRequests', ''),
                status='confirmed',
                booking_details=data,
                
                # AIOSell specific fields
                external_booking_id=external_booking_id,
                cm_booking_id=data.get('cmBookingId'),
                channel=data.get('channel'),
                segment=data.get('segment', 'OTA'),
                pah=pah,
                booked_on=booked_on,
                amount_before_tax=amount_before_tax,
                tax_amount=tax_amount,
                currency=currency,
                room_details=rooms_data
            )

            logger.info(f"Successfully created reservation {reservation.id} for booking ID {external_booking_id}")

            return Response({
                'success': True,
                'message': 'Reservation Updated Successfully',
                'reservation_id': str(reservation.id)
            }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error creating booking: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': 'Failed to create reservation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_booking_modification(data):
    """Handle booking modification from AIOSell"""
    try:
        with transaction.atomic():
            external_booking_id = data.get('bookingId')
            
            # Find existing reservation
            try:
                reservation = Reservation.objects.get(external_booking_id=external_booking_id)
            except Reservation.DoesNotExist:
                return Response({
                    'success': False,
                    'message': f'Reservation not found for booking ID: {external_booking_id}'
                }, status=status.HTTP_404_NOT_FOUND)

            # Parse updated dates if provided
            if data.get('checkin'):
                try:
                    reservation.check_in = datetime.strptime(data.get('checkin'), '%Y-%m-%d')
                except ValueError:
                    return Response({
                        'success': False,
                        'message': 'Invalid checkin date format. Use YYYY-MM-DD'
                    }, status=status.HTTP_400_BAD_REQUEST)

            if data.get('checkout'):
                try:
                    reservation.check_out = datetime.strptime(data.get('checkout'), '%Y-%m-%d')
                except ValueError:
                    return Response({
                        'success': False,
                        'message': 'Invalid checkout date format. Use YYYY-MM-DD'
                    }, status=status.HTTP_400_BAD_REQUEST)

            # Update amount if provided
            if data.get('amount'):
                amount_data = data.get('amount')
                reservation.total = amount_data.get('amountAfterTax', reservation.total)                
                reservation.amount_before_tax = amount_data.get('amountBeforeTax', reservation.amount_before_tax)
                reservation.tax_amount = amount_data.get('tax', reservation.tax_amount)
                reservation.currency = amount_data.get('currency', reservation.currency)

            # Update guest information if provided
            if data.get('guest'):
                guest_data = data.get('guest')
                guest_name = f"{guest_data.get('firstName', '')} {guest_data.get('lastName', '')}".strip()
                if guest_name and guest_data.get('phone'):
                    try:
                        user = get_or_create_user(guest_name, guest_data.get('phone'), guest_data.get('email'))
                        reservation.user = user
                    except ValidationError as e:
                        logger.warning(f"Failed to update guest info: {str(e)}")

            # Update other fields
            if data.get('specialRequests'):
                reservation.requests = data.get('specialRequests')

            # Update room details and guest count if provided            
            if data.get('rooms'):                
                total_guests = 0                
                for room in data.get('rooms', []):
                    occupancy = room.get('occupancy', {})
                    try:
                        adults = int(occupancy.get('adults', 0))
                        children = int(occupancy.get('children', 0))
                        total_guests += max(0, adults) + max(0, children)
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid occupancy data in room: {room}")                
                if total_guests > 0:
                    reservation.guests = total_guests
                reservation.room_details = data.get('rooms')                

            # Update PAH status
            if 'pah' in data:
                reservation.pah = data.get('pah')
                if not reservation.pah and reservation.paid == 0:
                    reservation.paid = reservation.total  # Mark as prepaid

            # Update booking details with new data
            reservation.booking_details = data
            reservation.save()

            logger.info(f"Successfully modified reservation {reservation.id} for booking ID {external_booking_id}")

            return Response({
                'success': True,
                'message': 'Reservation Modified Successfully'
            }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error modifying booking: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': 'Failed to modify reservation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_booking_cancellation(data):
    """Handle booking cancellation from AIOSell"""
    try:
        with transaction.atomic():
            external_booking_id = data.get('bookingId')
            
            # Find existing reservation
            try:
                reservation = Reservation.objects.get(external_booking_id=external_booking_id)
            except Reservation.DoesNotExist:
                return Response({
                    'success': False,
                    'message': f'Reservation not found for booking ID: {external_booking_id}'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if reservation can be cancelled
            if reservation.status == 'cancelled':
                return Response({
                    'success': True,
                    'message': 'Reservation already cancelled'
                }, status=status.HTTP_200_OK)

            if reservation.status == 'checked_in':
                return Response({
                    'success': False,
                    'message': 'Cannot cancel reservation - guest is already checked in'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Cancel the reservation
            reservation.status = 'cancelled'
            reservation.save()

            logger.info(f"Successfully cancelled reservation {reservation.id} for booking ID {external_booking_id}")

            return Response({
                'success': True,
                'message': 'Reservation Cancelled Successfully'
            }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error cancelling booking: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': 'Failed to cancel reservation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def get_property_by_hotel_code(hotel_code):
    """
    Find property by hotel code using the hotel_code field in Property model.
    Falls back to other matching strategies if direct hotel_code match fails.
    """
    try:
        if not hotel_code:
            logger.warning("Hotel code is empty or None")
            return None
            
        # First try to find by exact hotel_code field match
        property_obj = Property.objects.filter(hotel_code__iexact=hotel_code).first()
        
        if property_obj:
            logger.info(f"Found property {property_obj.name} by exact hotel_code match: {hotel_code}")
            return property_obj
        
        # Second try: case-insensitive partial match on hotel_code field
        property_obj = Property.objects.filter(hotel_code__icontains=hotel_code).first()
        
        if property_obj:
            logger.info(f"Found property {property_obj.name} by partial hotel_code match: {hotel_code}")
            return property_obj
        
        # Fallback 1: Try to find by property name (case-insensitive)
        property_obj = Property.objects.filter(name__icontains=hotel_code).first()
        
        if property_obj:
            logger.info(f"Found property {property_obj.name} by name match: {hotel_code}")
            return property_obj
        logger.warning(f"No property found for hotel code: {hotel_code}")
        return None
        
    except Exception as e:
        logger.error(f"Error finding property by hotel code {hotel_code}: {str(e)}")
        return None
