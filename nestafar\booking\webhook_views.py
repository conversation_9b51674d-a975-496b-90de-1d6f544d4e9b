"""
Channel Manager Webhook Views for handling reservation updates from multiple OTAs
"""
import logging
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from booking.channel_managers.factory import ChannelManagerFactory

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([AllowAny])
def channel_manager_webhook(request, channel_name):
    """
    Generic webhook endpoint for channel managers to send reservation updates.
    Routes requests to the appropriate channel manager adapter.

    Args:
        request: Django HTTP request
        channel_name: Name of the channel manager (e.g., 'aiosell', 'booking_com')
    """
    try:
        # Get the appropriate channel manager adapter
        adapter = ChannelManagerFactory.create_adapter(channel_name)

        if adapter is None:
            return Response({
                'success': False,
                'message': f'Unknown channel manager: {channel_name}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Process the webhook using the adapter
        return adapter.process_webhook(request)

    except Exception as e:
        logger.error(f"Channel manager webhook error ({channel_name}): {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def aiosell_webhook(request):
    """
    Legacy AIOSell webhook endpoint for backward compatibility.
    Redirects to the generic channel manager webhook.
    """
    return channel_manager_webhook(request, 'aiosell')


# Legacy support functions - these are now handled by channel manager adapters
# These functions are deprecated and will be removed in a future version

def handle_booking_creation(data):
    """
    DEPRECATED: Use channel manager adapters instead.
    Legacy function for backward compatibility.
    """
    from booking.channel_managers.factory import ChannelManagerFactory

    adapter = ChannelManagerFactory.create_adapter('aiosell')
    if adapter:
        return adapter.create_reservation(data)

    from rest_framework.response import Response
    from rest_framework import status
    return Response({
        'success': False,
        'message': 'AIOSell adapter not available'
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_booking_modification(data):
    """
    DEPRECATED: Use channel manager adapters instead.
    Legacy function for backward compatibility.
    """
    from booking.channel_managers.factory import ChannelManagerFactory

    adapter = ChannelManagerFactory.create_adapter('aiosell')
    if adapter:
        return adapter.modify_reservation(data)

    from rest_framework.response import Response
    from rest_framework import status
    return Response({
        'success': False,
        'message': 'AIOSell adapter not available'
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_booking_cancellation(data):
    """
    DEPRECATED: Use channel manager adapters instead.
    Legacy function for backward compatibility.
    """
    from booking.channel_managers.factory import ChannelManagerFactory

    adapter = ChannelManagerFactory.create_adapter('aiosell')
    if adapter:
        return adapter.cancel_reservation(data)

    from rest_framework.response import Response
    from rest_framework import status
    return Response({
        'success': False,
        'message': 'AIOSell adapter not available'
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

