"""nestafar URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
"""
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path("", include(("core.urls", "home"), namespace="home_urls")),
    path("admin/", admin.site.urls),
    path("core/", include(("core.urls", "core"), namespace="core_urls")),
    path("service/", include(("service.urls", "service"), namespace="service_urls")),
    path("stay/", include(("stay.urls", "stay"), namespace="stay_urls")),
    path("api-auth/", include("rest_framework.urls")),
    path("geo/", include(("geo.urls", "geo"), namespace="geo_urls")),
    path("notification/", include(("notification.urls", "notification"), namespace="notification_urls")),
    path("booking/", include(("booking.urls", "booking"), namespace="booking_urls")),
]
