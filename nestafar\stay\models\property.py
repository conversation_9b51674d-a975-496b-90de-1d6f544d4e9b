import uuid
from django.db import models
from django.forms.models import model_to_dict
import enum
from django.core.exceptions import ValidationError

class StayAmenities(enum.Enum):
    CCTV = "24/7 CCTV Surveillance"
    PARKING = "Parking Facility"
    INDOOR_FITNESS_CENTER = "Indoor Fitness Center"
    SMOKING_ROOMS = "Smoking Rooms"
    SWIMMING_POOL = "Swimming Pool"
    PETS_ALLOWED = "Pets Allowed"
    WIFI = "Free Wifi"
    SANITIZED = "Sanitized Rooms"
    KEY_CARD = "Key Card Access"
    POWER_BACKUP = "Power Backup"
    SPA = "Spa"
    BAR = "Bar"
    OPEN_DINING_AREA = "Open Dining Area"
    INDOOR_GAMES = "Indoor Games"
    HOUSEKEEPING = "Housekeeping"
    RESTAURANT = "Restaurant"
    OUTDOOR_ACTIVITIES = "Outdoor Activities"
    
    @staticmethod
    def __from__name__(name):
        for amenity in StayAmenities:
            if amenity.name == name:
                return amenity
        return None

    @staticmethod
    def list_amenities():
        return [(amenity.name, amenity.value) for amenity in StayAmenities]

    @staticmethod
    def validate_amenities(amenities):
        for amenity in amenities:
            if not StayAmenities.__from__name__(amenity):
                return False
        return True


class Property(models.Model):
    class Rating(models.IntegerChoices):
        ONE = 1
        TWO = 2
        THREE = 3
        FOUR = 4
        FIVE = 5

    class Type(models.IntegerChoices):
        FAMILY = 1
        ADVENTURE = 2
        LAVISH = 3
        BUDGET = 4
        COUPLE = 5

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    location = models.ForeignKey('geo.Location', on_delete=models.RESTRICT, related_name="property")
    name = models.CharField(max_length=200)
    avg_price = models.FloatField(blank=True, null=True)
    po_address = models.TextField(blank=True, null=True)
    photo = models.ImageField(upload_to='property', blank=True, null=True)
    type = models.PositiveSmallIntegerField(choices=Type.choices, default=Type.FAMILY)
    rooms = models.IntegerField()
    rating = models.PositiveSmallIntegerField(choices=Rating.choices, default=Rating.FIVE)
    description = models.TextField(blank=True, null=True)
    meal_cost = models.IntegerField(blank=True, null=True)
    directions = models.TextField(blank=True, null=True)
    staffs = models.ManyToManyField('core.PartnerProfile', related_name="properties")
    amenities = models.JSONField(null=True, blank=True)
    hotel_code = models.CharField(max_length=100, blank=True, null=True, help_text="Hotel/Property code in external system")

    class Meta:
        verbose_name_plural = "Properties"
        indexes = [
            models.Index(fields=['hotel_code'], name='property_hotel_code_idx'),
        ]

    def __str__(self):
        return self.name
        
    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def get_shortened_id(self):
        return str(self.id)[:4]

class PropertyMetaData(models.Model):
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name="metadata")
    key = models.CharField(max_length=100)
    value = models.TextField()

    class Meta:
        verbose_name_plural = "Property metadata"

    def __str__(self):
        return self.property.name + "_" + self.key



class PropertyReview(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name="review")
    review = models.TextField()
    image = models.ImageField(upload_to="review_images/", null=True, blank=True)
    rating = models.FloatField()
    user = models.ForeignKey('core.User', on_delete=models.CASCADE, related_name="property_review")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Review by {self.user} for {self.property}"

    def clean(self):
        # Ensure rating is between 1 and 5
        if not (1 <= self.rating <= 5):
            raise ValidationError("Rating must be between 1 and 5.")

    class Meta:
        ordering = ['-created_at']



class PropertyPartner(models.Model):
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name="property_partner")
    partner = models.ForeignKey('service.ServicePartner', on_delete=models.CASCADE, related_name="partner")
    commission = models.FloatField(default=0)
    delivery_charges = models.FloatField(default=0)
    pickup_charges = models.FloatField(default=0)
    name = models.CharField(max_length=100)
    in_house = models.BooleanField(default=False)

    def get_catalog(self):
        ## TODO: Deprecate this method
        items = self.partner.service_item.objects.filter(service__partner=self.partner)
        catalog = []
        for item in items:
            item_entry = model_to_dict(item)
            item_entry["id"] = item.id
            item_entry["price"] = round(item.price * (1 + self.commission / 100))
            if item_entry["addon"]:
                for addon, price in item_entry["addon"].items():
                    item_entry["addon"][addon] = round(price * (1 + self.commission/100))
                if item.image.name != "" and item.image.name is not None:
                    item_entry["image"] = item.image.url
                else:
                    item_entry["image"] = None
            catalog.append(item_entry)
        result = model_to_dict(self)
        result["catalog"] = catalog
        return result

    def __str__(self):
        return self.partner.name

