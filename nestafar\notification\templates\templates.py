def checkin_initiated_template(username, property_name, room_no, **kwargs):
    return f"Checkin Initiated for Room {room_no}", f"""Greetings {username}! 🌟
    We're delighted to inform you that your check-in at {property_name} has been successfully processed, and you've been assigned to Room {room_no}. 🏨
    To enhance your stay and explore our exclusive services, please click on the following link to complete your check-in: Complete Check-In.
    If you have any questions or need assistance during your stay, our friendly staff is here to help. We hope you have a wonderful and comfortable experience at {property_name}. Enjoy your stay! 🌟✨
    """

def checkout_template(username, property_name, **kwargs):
    return f"Checkout Processed for Property {property_name}", f"""Hello, {username}! 🌟
    We trust your stay at {property_name} was fantastic! As you get ready to check out, we hope it was a memorable experience. 🏨
    Looking forward to welcoming you back soon! 🌟✨"""

def order_accepted_template(username, order_id, **kwargs):
    subject = f"Your Order {order_id} has been Accepted."
    message = f"Hello, {username}! 🌟\n"\
              f"Order {order_id} is on its way! 🛍️"
    return subject, message

def order_cancelled_template(username, order_id, **kwargs):
    subject = f"Your Order {order_id} has been Cancelled."
    message = f"Hello, {username}! 🌟\n"\
              f"We regret to inform you that your order with ID {order_id} has been cancelled. 🛍️"
    return subject, message

def order_completed_template(username, order_id, **kwargs):
    subject = f"Your Order {order_id} has been Completed."
    message = f"Hello, {username}! 🌟\n"\
              f"We're delighted to inform you that your order with ID {order_id} has been successfully completed. 🛍️"
    return subject, message

def partner_order_placed_template(partner_name, order_id, room_no, guest_name, **kwargs):
    subject = f"New Order {order_id} Placed by {guest_name}."
    message = f"Hello, {partner_name}! 🌟\n"\
              f"We're thrilled to inform you that an order with ID {order_id} has been placed by {guest_name} in Room {room_no}. 🛍️\n"\
              f"Please ensure that the order is delivered to the guest's room promptly. 🌟✨"
    return subject, message

def pre_checkin_confirmed_template(property_owner_name, guest_name, expected_date, room_number, **kwargs):
    subject = f"Pre-Checkin Confirmed for {guest_name}."
    message = f"Hello {property_owner_name}, 🌟\n"\
              f"We're pleased to inform you that the pre-checkin for {guest_name} has been successfully completed in room(s) {room_number}. 🏨\n"\
              f"The guest is expected to arrive on {expected_date}. Please ensure that everything is prepared for their arrival.\n"\
              f"If you have any questions or need further assistance, feel free to reach out. 🌟✨"
    return subject, message


def pre_checkin_created_template(guest_name, property_owner_name, expected_date, room_number, **kwargs):
    subject = f"Pre-Checkin Created for {guest_name}."
    message = f"Hello {property_owner_name}, 🌟\n"\
              f"We're pleased to inform you that the pre-checkin for {guest_name} has been successfully created in room(s) {room_number}. 🏨\n"\
              f"The guest is expected to arrive on {expected_date}. Please ensure that everything is prepared for their arrival.\n"\
              f"If you have any questions or need further assistance, feel free to reach out. 🌟✨"
    return subject, message 