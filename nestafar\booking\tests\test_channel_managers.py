"""
Tests for the Channel Manager adapter system.
"""

from django.test import Test<PERSON>ase
from unittest.mock import Mock, patch
from booking.channel_managers.factory import ChannelManagerFactory
from booking.channel_managers.aiosell import AIOSellChannelManager
from booking.channel_managers.booking_com import BookingComChannelManager


class ChannelManagerFactoryTests(TestCase):
    """Test the Channel Manager Factory."""
    
    def test_create_aiosell_adapter(self):
        """Test creating AIOSell adapter."""
        adapter = ChannelManagerFactory.create_adapter('aiosell')
        self.assertIsInstance(adapter, AIOSellChannelManager)
        self.assertEqual(adapter.channel_name, 'aiosell')
    
    def test_create_booking_com_adapter(self):
        """Test creating Booking.com adapter."""
        adapter = ChannelManagerFactory.create_adapter('booking_com')
        self.assertIsInstance(adapter, BookingComChannelManager)
        self.assertEqual(adapter.channel_name, 'booking_com')
    
    def test_create_unknown_adapter(self):
        """Test creating unknown adapter returns None."""
        adapter = ChannelManagerFactory.create_adapter('unknown_ota')
        self.assertIsNone(adapter)
    
    def test_list_available_adapters(self):
        """Test listing available adapters."""
        adapters = ChannelManagerFactory.list_available_adapters()
        self.assertIn('aiosell', adapters)
        self.assertIn('booking_com', adapters)
        self.assertEqual(adapters['aiosell'], AIOSellChannelManager)
        self.assertEqual(adapters['booking_com'], BookingComChannelManager)
    
    def test_get_adapter_by_webhook_path(self):
        """Test getting adapter by webhook path."""
        # Test AIOSell path
        adapter = ChannelManagerFactory.get_adapter_by_webhook_path('/webhook/aiosell/')
        self.assertIsInstance(adapter, AIOSellChannelManager)
        
        # Test Booking.com path
        adapter = ChannelManagerFactory.get_adapter_by_webhook_path('/webhook/booking_com/')
        self.assertIsInstance(adapter, BookingComChannelManager)
        
        # Test invalid path
        adapter = ChannelManagerFactory.get_adapter_by_webhook_path('/invalid/path/')
        self.assertIsNone(adapter)


class AIOSellChannelManagerTests(TestCase):
    """Test the AIOSell Channel Manager adapter."""
    
    def setUp(self):
        self.adapter = AIOSellChannelManager()
    
    def test_validate_webhook_data_valid(self):
        """Test validation with valid AIOSell data."""
        data = {
            'action': 'book',
            'hotelCode': 'HOTEL123',
            'bookingId': 'BK123456',
            'checkin': '2024-01-01',
            'checkout': '2024-01-02',
            'guest': {'firstName': 'John', 'lastName': 'Doe'},
            'rooms': [{'occupancy': {'adults': 2}}],
            'amount': {'amountAfterTax': 100}
        }
        
        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
    
    def test_validate_webhook_data_missing_fields(self):
        """Test validation with missing required fields."""
        data = {
            'action': 'book',
            'hotelCode': 'HOTEL123'
            # Missing bookingId
        }
        
        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertFalse(is_valid)
        self.assertIn('Missing required fields', error)
    
    def test_parse_guest_data(self):
        """Test parsing guest data."""
        data = {
            'guest': {
                'firstName': 'John',
                'lastName': 'Doe',
                'email': '<EMAIL>',
                'phone': '1234567890',
                'address': {
                    'line1': '123 Main St',
                    'city': 'New York',
                    'state': 'NY',
                    'country': 'US',
                    'zipCode': '10001'
                }
            }
        }
        
        guest_info = self.adapter.parse_guest_data(data)
        
        self.assertEqual(guest_info['first_name'], 'John')
        self.assertEqual(guest_info['last_name'], 'Doe')
        self.assertEqual(guest_info['email'], '<EMAIL>')
        self.assertEqual(guest_info['phone'], '1234567890')
        self.assertEqual(guest_info['address']['line1'], '123 Main St')
    
    def test_parse_room_data(self):
        """Test parsing room data."""
        data = {
            'rooms': [
                {
                    'occupancy': {'adults': 2, 'children': 1}
                },
                {
                    'occupancy': {'adults': 1, 'children': 0}
                }
            ]
        }
        
        room_info = self.adapter.parse_room_data(data)
        
        self.assertEqual(room_info['total_guests'], 4)  # 2+1+1+0
        self.assertEqual(len(room_info['rooms_data']), 2)


class BookingComChannelManagerTests(TestCase):
    """Test the Booking.com Channel Manager adapter."""
    
    def setUp(self):
        self.adapter = BookingComChannelManager()
    
    def test_validate_webhook_data_valid(self):
        """Test validation with valid Booking.com data."""
        data = {
            'action': 'new_reservation',
            'reservation_id': 'BK123456',
            'hotel_id': 'HOTEL123',
            'guest': {'first_name': 'John', 'last_name': 'Doe'},
            'rooms': [{'adults': 2}],
            'dates': {'check_in': '2024-01-01', 'check_out': '2024-01-02'},
            'pricing': {'total_amount': 100}
        }
        
        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        # Check that action was normalized
        self.assertEqual(data['action'], 'book')
    
    def test_validate_webhook_data_invalid_action(self):
        """Test validation with invalid action."""
        data = {
            'action': 'invalid_action',
            'reservation_id': 'BK123456',
            'hotel_id': 'HOTEL123'
        }
        
        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertFalse(is_valid)
        self.assertIn('Invalid action', error)
    
    def test_parse_guest_data(self):
        """Test parsing Booking.com guest data."""
        data = {
            'guest': {
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>',
                'phone': '+1234567890',
                'address': {
                    'street': '123 Main St',
                    'city': 'New York',
                    'country': 'US',
                    'postal_code': '10001'
                }
            }
        }
        
        guest_info = self.adapter.parse_guest_data(data)
        
        self.assertEqual(guest_info['first_name'], 'John')
        self.assertEqual(guest_info['last_name'], 'Doe')
        self.assertEqual(guest_info['email'], '<EMAIL>')
        self.assertEqual(guest_info['phone'], '+1234567890')
        self.assertEqual(guest_info['address']['line1'], '123 Main St')
        self.assertEqual(guest_info['address']['zip_code'], '10001')
    
    def test_parse_room_data(self):
        """Test parsing Booking.com room data."""
        data = {
            'rooms': [
                {
                    'room_id': 'ROOM001',
                    'adults': 2,
                    'children': 1
                },
                {
                    'room_id': 'ROOM002',
                    'adults': 1,
                    'children': 0
                }
            ]
        }
        
        room_info = self.adapter.parse_room_data(data)
        
        self.assertEqual(room_info['total_guests'], 4)  # 2+1+1+0
        self.assertEqual(len(room_info['rooms_data']), 2)
