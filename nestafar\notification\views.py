from rest_framework.views import APIView
from nestafar.responses import CreateResponse, BadRequestResponse
from rest_framework.permissions import IsAuthenticated
from .models import FirebaseDeviceToken
from django.db import IntegrityError


class FirebaseTokenView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self,request):
        firebase_token=request.data.get('firebase_token')
        if not firebase_token:
            return BadRequestResponse(message="firebase_token is required")
        try:
            # Validate token format
            if not isinstance(firebase_token, str) or len(firebase_token) < 100:
                return BadRequestResponse(message="Invalid firebase token format")
            
            # Handle duplicate tokens
            existing_token = FirebaseDeviceToken.objects.filter(
                user=request.user, token=firebase_token
            ).first()
            if existing_token:
                return BadRequestResponse(message="Token already registered for this user")
                
            FirebaseDeviceToken.objects.create(user=request.user,token=firebase_token)
            return CreateResponse(message="Firebase token added successfully")
        except ValueError as e:
            return BadRequestResponse(f"Invalid token format: {str(e)}")
        except IntegrityError as e:
            return BadRequestResponse(message="Failed to save token due to database constraint")
        except Exception as e:
            return BadRequestResponse(message="Failed to process firebase token")