from .base_channel import BaseChannel
import firebase_admin
from firebase_admin import credentials
from firebase_admin.messaging import Message, Notification, AndroidConfig, AndroidNotification
from firebase_admin.messaging import send as send_firebase_message
from nestafar.utils.singleton import Singleton, synchronized
import threading
import logging
from ..models import FirebaseDeviceToken

lock = threading.Lock()

class FirebaseConnection(Singleton):
    logger = logging.getLogger(__name__)
    _connection = None

    def __init__(self) -> None:
        super(FirebaseConnection, self).__init__()
        self._init_connection()
    
    @synchronized(lock)
    def _init_connection(self):
        if self._connection is None:
            cred = credentials.Certificate("firebase_cred.json")
            firebase_admin.initialize_app(cred)
            self._connection = firebase_admin.get_app()
    
    def get_connection(self):
        return self._connection

class FirebaseChannel(BaseChannel):
    def __init__(self):
        self.connection=FirebaseConnection().get_connection()

    def create_message(self, user_id, message, heading=None):
        firebase_token=FirebaseDeviceToken.objects.filter(user_id=user_id).first()
        if firebase_token:
            self.msg=Message(
                    notification=Notification(
                        title=heading if heading else "Nestafar",
                        body=message
                    ),
                    token=firebase_token.token,
                    android=AndroidConfig(
                        priority='high',
                    ),
                    #TODO order id in data Notifications
                )
        
    def send(self):
        msg_id=send_firebase_message(self.msg)
        return True, msg_id        
    
    def send_message(self, user_id, message, heading=None):
        self.create_message(user_id, message, heading)
        if self.msg:
            return self.send()
        return False, "No firebase token found for user"
