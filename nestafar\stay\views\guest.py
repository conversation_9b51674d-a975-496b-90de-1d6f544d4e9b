from core.permissions import PropertyPermission
from rest_framework.permissions import IsA<PERSON>enticated
from rest_framework.views import APIView
from stay.models import Guest, Property
from stay.serializers import GuestSerializer
from nestafar.responses import SuccessResponse, NotFoundResponse, BadRequestResponse

class GuestHistoryView(APIView):
    permission_classes = [PropertyPermission]

    def get(self, request):
        property_id = request.property

        if not property_id:
            return BadRequestResponse(message="Property ID header is missing")

        try:
            property = Property.objects.get(id=property_id)
        except Property.DoesNotExist:
            return NotFoundResponse(message="Property not found")
        except ValueError:
            return BadRequestResponse(message="Invalid Property ID")

        guests = Guest.objects.filter(
            room__property=property, 
            checked_in=False,
            checked_out=True
        ).order_by('check_out_date')
        
        serializer = GuestSerializer(guests, many=True)
        return SuccessResponse(data=serializer.data, message="Checked-out guests and their stay history retrieved successfully")
