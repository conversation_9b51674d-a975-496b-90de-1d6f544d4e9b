from sys import flags
from rest_framework.decorators import action
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from core.permissions import PartnerPermission, PropertyPermission
from core.utils.sms import Messages, MessageTemplates
from booking.models import (
    Reservation, Profile, Pre<PERSON><PERSON>ckin, <PERSON>CheckinGuest,
    AllotedRoom, Payment
)
from stay.models import Room, Property, Guest
from booking.serializers import (
    ReservationSerializer, ProfileSerializer, PreCheckinSerializer,
    PreCheckinGuestSerializer, AllotedRoomSerializer, PaymentSerializer, RoomSerializer, PropertySerializer
)
from datetime import datetime
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from core.models import PartnerProfile, User
from django.db import transaction
from notification.models import NotificationCategory
from notification.tasks import send_notification
from django.utils import timezone
import uuid
from core.utils import get_or_create_user
from nestafar.responses import (
    SuccessResponse, CreateResponse, BadRequestResponse,
    NotFoundResponse, InternalServerErrorResponse
)
import logging
from django.db import IntegrityError

logger = logging.getLogger(__name__)

class ReservationViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions to manage reservations.
    """
    serializer_class = ReservationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Retrieve reservations for the authenticated user only."""
        return Reservation.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Create a new reservation."""
        property = serializer.validated_data['property']
        check_in = serializer.validated_data['check_in']
        check_out = serializer.validated_data['check_out']
        
        if not self.is_property_available(property, check_in, check_out):
            raise ValidationError("This property is not available during the selected dates.")
        
        serializer.save(user=self.request.user)

    def is_property_available(self, property, check_in, check_out):
        """
        Check if the given property is available for the specified date range.
        A property is available if there are sufficient rooms available.
        """
        conflicting_reservations = Reservation.objects.filter(
            property=property,
            status__in=['confirmed', 'checked_in'],
            check_in__lt=check_out,
            check_out__gt=check_in
        )
        return not conflicting_reservations.exists()

    @action(detail=False, methods=['get'])
    def availability(self, request):
        """
        Check the availability of properties based on check-in and check-out dates.
        """
        check_in = request.query_params.get('check_in')
        check_out = request.query_params.get('check_out')
        property_id = request.query_params.get('property_id')

        if not check_in or not check_out:
            return BadRequestResponse(message="Please provide both check-in and check-out dates.")

        try:
            check_in_date = datetime.fromisoformat(check_in.replace("Z", "+00:00"))
            check_out_date = datetime.fromisoformat(check_out.replace("Z", "+00:00"))
        except ValueError:
            return BadRequestResponse(message="Invalid date format. Use ISO-8601 format: YYYY-MM-DDTHH:MM:SSZ")

        if check_in_date >= check_out_date:
            return BadRequestResponse(message="Check-out date must be after check-in date.")

        if property_id:
            try:
                property = Property.objects.get(id=property_id)
                is_available = self.is_property_available(property, check_in_date, check_out_date)
                return SuccessResponse(data={"is_available": is_available})
            except Property.DoesNotExist:
                return NotFoundResponse(message="Property not found.")
        else:
            available_properties = self.get_available_properties(check_in_date, check_out_date)
            return SuccessResponse(data=available_properties)

    def get_available_properties(self, check_in, check_out):
        """Get all available properties within the check-in and check-out time period."""
        reserved_properties = Reservation.objects.filter(
            status__in=['confirmed', 'checked_in'],
            check_in__lt=check_out,
            check_out__gt=check_in
        ).values_list('property_id', flat=True)

        available_properties = Property.objects.exclude(id__in=reserved_properties)
        return PropertySerializer(available_properties, many=True).data
    
    @action(detail=False, methods=['get'], permission_classes=[PropertyPermission])
    def property_reservations(self, request):
        """Retrieve all reservations for the authenticated user's property."""
        try:
            reservations = Reservation.objects.filter(property=request.property)
            page = self.paginate_queryset(reservations)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            serializer = self.get_serializer(reservations, many=True)
            return SuccessResponse(data=serializer.data)
        except Exception as e:
            return InternalServerErrorResponse(message=str(e))

class ProfileViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions to manage profiles
    and their associated images.
    """
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Profile.objects.filter(partner__user=self.request.user)

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        partner = PartnerProfile.objects.filter(user=request.user).first()
        if not partner:
            return BadRequestResponse(message="No partner profile found for the authenticated user.")

        request_data = request.data.copy()
        serializer = self.get_serializer(data=request_data)
        serializer.is_valid(raise_exception=True)
        profile = serializer.save(partner=partner)
        
        headers = self.get_success_headers(serializer.data)
        return CreateResponse(data=self.get_serializer(profile).data, headers=headers)

class PreCheckinViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions to manage pre-checkins.
    """
    serializer_class = PreCheckinSerializer
    permission_classes = [PropertyPermission]

    def get_queryset(self):
        """
        Override get_queryset to apply filters based on query parameters.
        """
        queryset = PreCheckin.objects.filter(property=self.request.property)

        # Get query parameters
        status = self.request.query_params.get('status')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        # Apply status filter
        if status:
            queryset = queryset.filter(status__in=status.split(','))

        # Apply date range filter
        if start_date and end_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

                if start_date > end_date:
                    raise ValidationError("End date must be after start date.")

                start_datetime = timezone.make_aware(datetime.combine(start_date, datetime.min.time()))
                end_datetime = timezone.make_aware(datetime.combine(end_date, datetime.max.time()))

                queryset = queryset.filter(expected_checkin__range=(start_datetime, end_datetime))
            except ValueError as e:
                raise ValidationError("Invalid date format. Use YYYY-MM-DD") from e

        return queryset

    def list(self, request, *args, **kwargs):
        """Override list method to use standard response format."""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = self.get_serializer(queryset, many=True)
            return SuccessResponse(
                data=serializer.data,
                message="Pre-checkin requests retrieved successfully."
            )
        except ValidationError as e:
            return BadRequestResponse(message=str(e))
        except Exception as e:
            return InternalServerErrorResponse(message=str(e))

    def retrieve(self, request, *args, **kwargs):
        """Override retrieve method to use standard response format."""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return SuccessResponse(
                data=serializer.data,
                message="Pre-checkin request retrieved successfully."
            )
        except PreCheckin.DoesNotExist:
            return NotFoundResponse(message="Pre-checkin not found.")
        except Exception as e:
            return InternalServerErrorResponse(message=str(e))

    def get_paginated_response(self, data):
        """Override pagination response to use standard format."""
        assert self.paginator is not None
        return SuccessResponse(
            data=self.paginator.get_paginated_data(data),
            message="Pre-checkin requests retrieved successfully."
        )

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create a new pre-checkin with primary guest."""
        try:
            with transaction.atomic():
                # Validate and create pre-checkin
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                
                # Get user data from validated data
                user_data = request.data.get('primary_guest')   
                if not user_data or not user_data.get('phone') or not user_data.get('name'):
                    raise ValidationError("Name and phone are required")
                # Check if user exists first
                try:
                    user = User.objects.get(phone=user_data['phone'])
                except User.DoesNotExist:
                    # Only create user if doesn't exist
                    user = get_or_create_user(
                        name=user_data['name'],
                        phone=user_data['phone']
                    )
                
                # Check if user already has a pending pre-checkin
                existing_precheckin = PreCheckinGuest.objects.filter(
                    user=user,
                    pre_checkin__property=self.request.property,
                    pre_checkin__status__in=['pending', 'partial', 'confirmed', 'checked_in']
                ).exists()
                
                if existing_precheckin:
                    raise ValidationError("User already has an ongoing pre-checkin request")
                
                # Create pre-checkin with pending status
                pre_checkin = serializer.save(
                    property=self.request.property,
                    status='pending'
                )
                
                # Create primary guest entry
                PreCheckinGuest.objects.create(
                    pre_checkin=pre_checkin,
                    user=user,
                    is_primary=True,
                    age=user_data.get('age', 18)
                )

                # Send notification to primary guest
                send_notification.delay(
                    user_id = user.id,
                    event = 'PRE_CHECKIN_CREATED',
                    data = {
                        'guest_name': user.name,
                        'property_owner_name': pre_checkin.property.name,
                        'expected_date': pre_checkin.expected_checkin.strftime('%d %b %Y'),
                        'room_number': pre_checkin.alloted_rooms.first().room.room_no if pre_checkin.alloted_rooms.exists() else 'N/A'
                    }
                )
                
                return CreateResponse(
                    data=self.get_serializer(pre_checkin).data,
                    message="Pre-checkin created successfully with primary guest."
                )
                
        except ValidationError as e:
            transaction.set_rollback(True)
            return BadRequestResponse(message=str(e))
        except Exception as e:
            transaction.set_rollback(True)
            return InternalServerErrorResponse(message=str(e))

    @transaction.atomic
    @action(detail=True, methods=['post'])
    def upload_id(self, request, pk=None):
        """Upload ID and create guest entry."""
        try:
            with transaction.atomic():
                pre_checkin = self.get_object()
                
                # Validate required data
                guest_data = request.data
                if not guest_data.get('name') or not guest_data.get('phone'):
                    raise ValidationError("Guest name and phone are required")
                
                if not request.FILES.get('id_proof'):
                    raise ValidationError("ID proof file is required")
                
                # Check if user exists first
                try:
                    user = User.objects.get(phone=guest_data['phone'])
                except User.DoesNotExist:
                    # Only create user if doesn't exist
                    user = get_or_create_user(
                        name=guest_data['name'],
                        phone=guest_data['phone']
                    )
                
                # Check if guest already exists in this pre-checkin
                guest_exists = PreCheckinGuest.objects.filter(
                    pre_checkin=pre_checkin,
                    user=user
                ).exists()
                
                if guest_exists:
                    raise ValidationError("Guest already exists in this pre-checkin")
                
                # Create guest entry
                guest = PreCheckinGuest.objects.create(
                    pre_checkin=pre_checkin,
                    user=user,
                    is_primary=False,
                    age=guest_data.get('age', 18),
                    id_proof=request.FILES['id_proof']
                )
                
                # Update pre-checkin status to partial
                pre_checkin.status = 'partial'
                pre_checkin.save()
                
                return CreateResponse(
                    data=PreCheckinGuestSerializer(guest).data,
                    message="Guest added and ID proof uploaded successfully."
                )
        except ValidationError as e:
            transaction.set_rollback(True)
            return BadRequestResponse(message=str(e))
        except PreCheckin.DoesNotExist:
            transaction.set_rollback(True)
            return NotFoundResponse(message="Pre-checkin not found")
        except Exception as e:
            transaction.set_rollback(True)
            return InternalServerErrorResponse(message=str(e))

    @transaction.atomic
    @action(detail=True, methods=['get'])
    def arrived(self, request, pk=None):
        """Mark pre-checkin as arrived and allocate rooms."""

        if not request.user.is_partner:
            return BadRequestResponse(message="Only hotel can mark pre-checkin as arrived")
        try:
            pre_checkin = self.get_object()

            # Validate pre-checkin status
            if pre_checkin.status not in ['confirmed']:
                raise ValidationError("Pre-checkin cannot be marked as arrived in current status")
            
            pre_checkin.status = 'arrived'
            pre_checkin.save()
            return SuccessResponse(
                data=PreCheckinSerializer(pre_checkin).data,
                message="Pre-checkin marked as arrived successfully."
            )
        except ValidationError as e:
            transaction.set_rollback(True)
            return BadRequestResponse(message=str(e))
        except PreCheckin.DoesNotExist:
            transaction.set_rollback(True)
            return NotFoundResponse(message="Pre-checkin not found")
        except Exception as e:
            transaction.set_rollback(True)
            return InternalServerErrorResponse(message=str(e))


    @transaction.atomic
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm pre-checkin and allocate rooms."""
        try:
            with transaction.atomic():
                pre_checkin = self.get_object()
                
                # Validate pre-checkin status
                if pre_checkin.status not in ['pending', 'partial']:
                    raise ValidationError("Pre-checkin cannot be confirmed in current status")
                
                # Check if all guests have uploaded ID proofs
                guests_without_id = PreCheckinGuest.objects.filter(
                    pre_checkin=pre_checkin,
                    id_proof__isnull=True
                ).exists()
                
                if guests_without_id:
                    raise ValidationError("All guests must upload ID proof before confirmation")
                
                # Get rooms to allocate
                room_ids = request.data.get('room_ids', [])
                if not room_ids:
                    raise ValidationError("Room IDs are required for confirmation")
                
                if len(room_ids) != pre_checkin.number_of_rooms:
                    raise ValidationError(
                        f"Number of rooms ({len(room_ids)}) does not match required rooms ({pre_checkin.number_of_rooms})"
                    )
                
                # Check room availability and allocate
                for room_id in room_ids:
                    try:
                        room = Room.objects.select_for_update().get(
                            id=room_id,
                            property=self.request.property
                        )
                        
                        # Check if room is already allocated
                        is_allocated = AllotedRoom.objects.filter(
                            room=room,
                            pre_checkin__status__in=['confirmed', 'checked_in'],
                            pre_checkin__expected_checkin__lt=pre_checkin.get_expected_checkout(),
                            pre_checkin__expected_checkin__gt=pre_checkin.expected_checkin
                        ).exists()
                        
                        if is_allocated:
                            raise ValidationError(f"Room {room.room_no} is not available for the selected dates")
                        
                        # Create room allocation
                        AllotedRoom.objects.create(
                            pre_checkin=pre_checkin,
                            room=room
                        )
                        
                    except Room.DoesNotExist:
                        raise ValidationError(f"Room with id {room_id} not found")
                
                # Update pre-checkin status
                pre_checkin.status = 'confirmed'
                pre_checkin.save()
                
                return SuccessResponse(
                    data=self.get_serializer(pre_checkin).data,
                    message="Pre-checkin confirmed and rooms allocated successfully."
                )
        except ValidationError as e:
            transaction.set_rollback(True)
            return BadRequestResponse(message=str(e))
        except Exception as e:
            transaction.set_rollback(True)
            return InternalServerErrorResponse(message=str(e))

    @transaction.atomic
    @action(detail=True, methods=['post'])
    def resend(self, request, pk=None):
        """Notify guests about pre-checkin confirmation."""
        try:
            pre_checkin = self.get_object()
            # Notify all guests
            for guest in pre_checkin.pre_checkin_guests.all():
                result, response = send_notification(
                    user_id = guest.user.id,
                    event='PRE_CHECKIN_CREATED',
                    data = {
                        'guest_name': guest.user.name,
                        'property_owner_name': pre_checkin.property.name,
                        'expected_date': pre_checkin.expected_checkin.strftime('%d %b %Y'),
                        'room_number': pre_checkin.alloted_rooms.first().room.room_no if pre_checkin.alloted_rooms.exists() else 'N/A'
                    }
                )
            
            return SuccessResponse(message="Guests notified successfully.")
        except PreCheckin.DoesNotExist:
            return NotFoundResponse(message="Pre-checkin not found.")
        except ObjectDoesNotExist:  
            return NotFoundResponse(message="Guest not found.")
        except Exception as e:
            return BadRequestResponse(message=str(e))
        
    @transaction.atomic
    @action(detail=True, methods=['get', 'post'])
    def rooms(self, request, pk=None):
        """Retrieve available rooms for a pre-checkin."""
        try:
            pre_checkin = self.get_object()
            expected_checkout = pre_checkin.get_expected_checkout()
            # get confirmed and checked-in precheckins which overlap with the current precheckin
            overlapping_precheckins = PreCheckin.objects.filter(
                property=pre_checkin.property,
                status__in=['confirmed', 'checked_in'],
                expected_checkin__lt=expected_checkout,
                stay_duration__gt=0,
            ).exclude(id=pre_checkin.id)

            # filter out precheckins which are not overlapping

            result = overlapping_precheckins

            for overlapping_precheckin in overlapping_precheckins:
                overlap_expected_checkout = overlapping_precheckin.get_expected_checkout()
                if pre_checkin.expected_checkin > overlap_expected_checkout:
                    overlapping_precheckins = result.exclude(id=overlapping_precheckin.id)

            # get alloted rooms for the overlapping precheckins
            alloted_rooms = AllotedRoom.objects.filter(
                pre_checkin__in=overlapping_precheckins
            ).values_list('room_id', flat=True)

            # get all available rooms for the current precheckin
            available_rooms = Room.objects.filter(
                property=pre_checkin.property,
            ).exclude(id__in=alloted_rooms)

            if not available_rooms.exists():
                return NotFoundResponse(message="No available rooms found.")
            # serialize the available rooms
            serializer = RoomSerializer(available_rooms, many=True)
            return SuccessResponse(data=serializer.data)
        except PreCheckin.DoesNotExist:
            return NotFoundResponse(message="Pre-checkin not found.")
        except Exception as e:
            return BadRequestResponse(message=str(e))
        except ValidationError as e:    
            return BadRequestResponse(message=str(e))
    
    @transaction.atomic
    @action(detail=True, methods=['post'])
    def add_payment(self, request, pk=None):
        """Add a payment to the pre-checkin."""
        try:
            pre_checkin = self.get_object()
            serializer = PaymentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            payment = serializer.save(pre_checkin=pre_checkin)
            
            # Update pre-checkin payment status
            pre_checkin.amount_paid += payment.amount
            pre_checkin.pending_balance = pre_checkin.total_amount - pre_checkin.amount_paid
            
            if pre_checkin.pending_balance <= 0:
                pre_checkin.payment_status = 'completed'
            elif pre_checkin.amount_paid > 0:
                pre_checkin.payment_status = 'partial'
            
            pre_checkin.save()
            
            return CreateResponse(
                data=PaymentSerializer(payment).data,
                message="Payment added successfully."
            )
        except Exception as e:
            return BadRequestResponse(message=str(e))

    @transaction.atomic
    @action(detail=True, methods=['post'])
    def allocate_room(self, request, pk=None):
        """Allocate a room to a guest."""
        try:
            pre_checkin = self.get_object()
            room_id = request.data.get('room_id')
            guest_id = request.data.get('guest_id')
            
            try:
                room = Room.objects.get(id=room_id, property=self.request.property)
                guest = PreCheckinGuest.objects.get(id=guest_id, pre_checkin=pre_checkin)
            except (Room.DoesNotExist, PreCheckinGuest.DoesNotExist):
                return NotFoundResponse(message="Room or guest not found.")
            
            # Create room allocation
            alloted_room = AllotedRoom.objects.create(
                pre_checkin=pre_checkin,
                room=room
            )
            
            # Update guest's room
            guest.room = room
            guest.save()
            
            return CreateResponse(
                data=AllotedRoomSerializer(alloted_room).data,
                message="Room allocated successfully."
            )
        except Exception as e:
            return BadRequestResponse(message=str(e))

    @transaction.atomic
    @action(detail=True, methods=['post'])
    def checkout(self, request, pk=None):
        """Check out a guest from a room."""
        try:
            pre_checkin = self.get_object()
            rooms = AllotedRoom.objects.filter(
                pre_checkin=pre_checkin,
            ).select_related('room')
            if not rooms.exists():
                return NotFoundResponse(message="No rooms allocated for this pre-checkin.")
            with transaction.atomic():
                for room in rooms:
                    guests = Guest.objects.filter(
                        room=room.room,
                        checked_in=True
                    )
                    for guest in guests:
                        guest.checked_in = False
                        guest.checked_out = True
                        guest.check_out_date = timezone.now()
                        guest.save()
                    # Update room status
                    room.room.running_total = 0
                    room.room.checked_in = False
                    room.room.occupied = False
                    room.room.save()
                pre_checkin.status = 'checked_out'
                pre_checkin.save()
                return SuccessResponse(message="Guest checked out successfully.")
        except Exception as e:
            return BadRequestResponse(message=str(e))



class GuestPreCheckinViewSet(viewsets.ViewSet):
    """
    A viewset that provides actions for guests to manage their pre-checkins.
    """
    permission_classes = [IsAuthenticated]

    def retrieve(self, request):
        try:
            pre_checkin = PreCheckinGuest.objects.get(user=request.user.id, status='pending').pre_checkin
            serializer = PreCheckinSerializer(pre_checkin)
            return SuccessResponse(data=serializer.data)
        except PreCheckin.DoesNotExist:
            return NotFoundResponse(message="No pending pre-checkin found.")
        except Exception as e:
            return InternalServerErrorResponse(message=str(e))

    @action(detail=True, methods=['PATCH'])
    def setup(self, request, pk=None):
        try:
            pre_checkin = PreCheckin.objects.get(pk=pk)
            data = request.data

            checkin_time = data.get('checkin_time')
            if checkin_time:
                try:
                    checkin_time_obj = datetime.strptime(checkin_time, '%H:%M:%S').time()
                    checkin_datetime = datetime.combine(pre_checkin.expected_checkin.date(), checkin_time_obj)
                    checkin_datetime = timezone.make_aware(checkin_datetime, timezone.get_current_timezone())
                    pre_checkin.expected_checkin = checkin_datetime
                except ValueError:
                    return BadRequestResponse(message="Invalid time format. Use HH:MM:SS.")

            pre_checkin.guest_address = data.get('guest_address', pre_checkin.guest_address)
            pre_checkin.special_requests = data.get('special_requests', pre_checkin.special_requests)
            pre_checkin.status = 'partial'
            
            pre_checkin.save()

            return SuccessResponse(
                message="Pre-checkin details updated successfully.",
                data=PreCheckinSerializer(pre_checkin).data
            )
        except PreCheckin.DoesNotExist:
            return BadRequestResponse(message="Pre-checkin not found.")
        except Exception as e:
            return InternalServerErrorResponse(message=str(e))
    
    @action(detail=True, methods=['PATCH'])
    def cancel(self, request, pk=None):
        try:
            pre_checkin = PreCheckin.objects.get(pk=pk)

            if pre_checkin.status == 'cancelled':
                return BadRequestResponse(message="This pre-checkin is already cancelled.")

            with transaction.atomic():
                # Cancel the pre-checkin
                pre_checkin.status = 'cancelled'
                pre_checkin.save()
            return SuccessResponse(message="Pre-checkin cancelled successfully.")
        except PreCheckin.DoesNotExist:
            return BadRequestResponse(message="Pre-checkin not found.")
        except Exception as e:
            return InternalServerErrorResponse(message=str(e))

    @action(detail=False, methods=['POST'])
    def upload_id(self, request, pk=None):
        """
        Upload ID proof for a pre-checkin.
        Handles duplicate checking and user verification.
        """
        try:
            pre_checkin = PreCheckin.objects.get(pk=request.data.get('pre_checkin'))
            guest_name = request.data.get('name')
            guest_phone = request.data.get('phone')
            age = request.data.get('age', 18)
            id_proof = request.FILES.get('id_proof')

            user = get_or_create_user(guest_name, guest_phone)
            guest = PreCheckinGuest.objects.filter(
                pre_checkin=pre_checkin,
                user=user
            ).first()
            
            if not guest:
                guest = PreCheckinGuest.objects.create(
                    pre_checkin=pre_checkin,
                    user=user,
                    id_proof=id_proof,
                    is_primary=False,
                    age=age
                )
            guest.age = age
            guest.id_proof = id_proof
            guest.save()

            return SuccessResponse(
                message="ID proof uploaded successfully.",
                data=PreCheckinGuestSerializer(guest).data
            )
        except IntegrityError as e:
            return BadRequestResponse(
                message= 'Database integrity error. Possible duplicate entry.',
                data = str(e)
            )

        except ValidationError as e:
            return BadRequestResponse(
                message= 'Validation error',
                data = str(e)
            )

        except Exception as e:
            return InternalServerErrorResponse(
                message= 'An unexpected error occurred',
                data = str(e)
            )