from booking.serializers import AllotedRoomSerializer, PreCheckinSerializer
from stay.serializers import CheckInSerializer,  GuestSerializer, CheckinRequest, CheckinRequestSerializer
from core.serializers import UserProfileSerializer
from datetime import datetime, timedelta
from core.models import User
from django.utils.crypto import get_random_string
from nestafar.responses import SuccessResponse, BadRequestResponse,NotFoundResponse
from rest_framework.views import APIView
from core.permissions import PartnerPermission
from core.utils.sms import Messages, MessageTemplates
from rest_framework.permissions import IsAuthenticated
from stay.models import Room, CheckinRequest, Property, Guest
from django.db import transaction
from notification.models import NotificationCategory
from notification.tasks import send_notification
from core.utils import get_or_create_user
from booking.models import AllotedRoom, PreCheckin, PreCheckinGuest
from django.utils import timezone

class InitiateCheckin(APIView):
    permission_classes = [PartnerPermission]

    def add_guest(self, room, guest_data, checkin_key, is_precheckin=False, expected_checkin=None):
        """
        Adds a guest to the room. Handles both immediate check-in and pre-checkin cases.
        """
        try:
            with transaction.atomic():
                # Create or fetch the user instance
                user_instance = get_or_create_user(guest_data['name'], guest_data['phone'])

                # Mark any previous unchecked-out entries for this user as checked out
                existing_guests = Guest.objects.filter(user=user_instance).exclude(checked_out=True)
                for g in existing_guests:
                    g.checked_out = True
                    g.checked_in = False
                    g.save(update_fields=['checked_out', 'checked_in'])

                # Handle pre-checkin scenario
                if is_precheckin:
                    pre_checkin = PreCheckin.objects.create(
                        room=room,
                        guest=user_instance.guest_profile,
                        expected_checkin=expected_checkin,
                        amount_paid=0,
                        pending_balance=0,
                        payment_status='pending',
                        status='pending'
                    )
                    return pre_checkin

                # Immediate check-in scenario
                guest = Guest.objects.create(
                    room=room,
                    checkin_key=checkin_key,
                    user=user_instance,
                    checked_in=True,
                    check_in_date=datetime.now()
                )

                # Send notifications
                send_notification.delay(
                    user_instance.id,
                    NotificationCategory.USER_CHECKIN_INITIATED.name,
                    {
                        'username': user_instance.name,
                        'room_no': room.room_no,
                        'property_name': room.property.name,
                    },
                )
                msg = Messages(user_instance.phone.national_number)
                msg.send_bulk_sms(MessageTemplates.CHECKIN.value, f'{room.property.name}, {room.room_no}')
                return guest
        except Exception as e:
            raise Exception(f"Error adding guest: {str(e)}")

    def post(self, request):
        """
        Initiates a check-in process. Creates PreCheckin objects if the check-in date is in the future.
        """
        try:
            serializer = CheckInSerializer(
                data=request.data, context={"property": request.property}
            )
            if not serializer.is_valid():
                return BadRequestResponse(
                    data=serializer.errors, message="Invalid data"
                )

            data = serializer.validated_data
            checkin_key = get_random_string(length=32)
            _property = request.property

            with transaction.atomic():
                room = Room.objects.get(id=data["room_id"], property=_property)
                if room.checked_in:
                    return BadRequestResponse(
                        message="Room already checked in"
                    )

                guests = []
                pre_checkins = []
                for guest_data in data["guests"]:
                    # Check if the check-in date is later than today
                    expected_checkin = data.get("expected_checkin", datetime.now())
                    if expected_checkin > datetime.now():
                        pre_checkin = self.add_guest(
                            room, guest_data, checkin_key, is_precheckin=True, expected_checkin=expected_checkin
                        )
                        pre_checkins.append(pre_checkin)
                    else:
                        guest = self.add_guest(room, guest_data, checkin_key)
                        guests.append(guest)

                # Update room status if it's an immediate check-in
                if guests:
                    room.checked_in = True
                    room.save(update_fields=["checked_in"])

                response_data = {
                    "immediate_guests": GuestSerializer(guests, many=True).data,
                    "pre_checkins": [
                        {
                            "guest_name": pc.guest.user.name,
                            "room_no": pc.room.room_no,
                            "expected_checkin": pc.expected_checkin,
                            "status": pc.status,
                        }
                        for pc in pre_checkins
                    ],
                }
                return SuccessResponse(
                    data=response_data,
                    message="Checkin initiated successfully",
                )
        except Room.DoesNotExist:
            return NotFoundResponse(message="Room not found")
        except Exception as e:
            return BadRequestResponse(data=str(e))

    def get(self, request):
        try:
            room_id = request.query_params.get('room_id')
            room = Room.objects.get(pk=room_id)
            latest_guest = Guest.objects.filter(room=room, room__checked_in=True, checked_out=False).last()
            if not latest_guest:
                return BadRequestResponse(message="No Checked In Guests found. Please check out.")
            checkin_initiated_guests = Guest.objects.filter(checkin_key=latest_guest.checkin_key)
            serialized_guests = GuestSerializer(checkin_initiated_guests, many=True).data
            response = {
                'room_id': room_id,
                'guests': serialized_guests
            }
            return SuccessResponse(data=response)
        except Exception as e:
            return BadRequestResponse(data=str(e))

    def put(self, request):
        try:
            serializer = CheckInSerializer(data=request.data,
                                           context={"property": request.property})
            if not serializer.is_valid():
                return BadRequestResponse(data=serializer.errors, message="Invalid data")

            data = serializer.validated_data
            room = Room.objects.get(pk=data.get('room_id'))
            latest_guest = Guest.objects.filter(room=room, room__checked_in=True, checked_out=False).latest(
                'created_at')
            checkin_initiated_guests = Guest.objects.filter(checkin_key=latest_guest.checkin_key)
            guests = []
            with transaction.atomic():
                for guest in data.get('guests'):
                    if not checkin_initiated_guests.filter(user__phone=guest['phone']).exists():
                        guests.append(self.add_guest(room, guest, latest_guest.checkin_key))
            return SuccessResponse(data=GuestSerializer(guests, many=True).data,
                                   message="Updated successfully")
        except Exception as e:
            return BadRequestResponse(data=str(e))


class Checkin(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # If pending precheckins exist, address it.
            pre_checkin_guest = PreCheckinGuest.objects.filter(user=request.user, pre_checkin__status='pending').last()
            if pre_checkin_guest:
                serializer = PreCheckinSerializer(pre_checkin_guest.pre_checkin)
                return SuccessResponse(message="PRECHECKIN_FOUND", data=serializer.data)
            
            # Check if any confirmed pre-checkins exists for today and create guest entries to checkin in the case.
            pre_checkin_guest = PreCheckinGuest.objects.filter(user=request.user, pre_checkin__status__in=['arrived', 'confirmed']).order_by('-created_at').last()
            if pre_checkin_guest:
                pre_checkin = pre_checkin_guest.pre_checkin
                checkin_time_diff = abs(timezone.now() - pre_checkin.expected_checkin)
                if checkin_time_diff > timedelta(days=1) or pre_checkin.status != 'arrived':
                    return SuccessResponse(message="NO_PRECHECKIN_TODAY")
                else:
                    rooms = AllotedRoom.objects.filter(pre_checkin=pre_checkin)
                    if not rooms.exists():
                        return SuccessResponse(message="ROOM_NOT_ALLOTED")
                    else:
                        serializer = AllotedRoomSerializer(rooms, many=True)
                        return SuccessResponse(message="ROOMS_ALLOTED", data=serializer.data)
                    
            # If no pre-checkin exists, check for a guest entry.
            # Check if the user has a guest entry that is not checked in or checked out.
            guest = Guest.objects.filter(user=request.user, checked_in=False, checked_out=False).last()
            if guest:
                with transaction.atomic():
                    guest.check_in_date = datetime.now()
                    guest.checked_in = True
                    guest.room.checked_in = True
                    guest.room.save()
                    guest.save()
                serializer = UserProfileSerializer(guest.user)
                return SuccessResponse(message="CHECKIN_SUCCESS", data=serializer.data)
            else:
                if PreCheckinGuest.objects.filter(user=request.user, pre_checkin__status='partial').exists():
                    return SuccessResponse(message="NO_PRECHECKIN_TODAY")
                else:
                    return BadRequestResponse(message="NO_PRECHECKIN")
        except Room.DoesNotExist:
            return NotFoundResponse(message="ROOM_NOT_FOUND")
        except Guest.DoesNotExist:
            return NotFoundResponse(message="GUEST_NOT_FOUND")
        except Exception as e:
            return BadRequestResponse(message="ERROR", data=str(e))


class CompleteCheckin(APIView):
    permission_classes = [IsAuthenticated]

    @transaction.atomic
    def post(self, request):
        try:
            serializer = AllotedRoomSerializer(data=request.data)
            if not serializer.is_valid():
                return BadRequestResponse(data=serializer.errors, message="Invalid data")
            data = serializer.validated_data
            pre_checkin = data.get('pre_checkin')
            room = Room.objects.get(pk=request.data.get('room').get('id'))

            precheckin_guest = PreCheckinGuest.objects.get(
                user=request.user,
                pre_checkin=pre_checkin
            )

            pre_checkin.status = 'checked_in'
            pre_checkin.save()
            
            if precheckin_guest:
                Guest.objects.create(
                    user=precheckin_guest.user,
                    room=room,
                    checked_in=False,
                    checked_out=False,
                    check_in_date=datetime.now()
                )
        except PreCheckinGuest.DoesNotExist:
            return NotFoundResponse(message="PreCheckinGuest not found")
        except AllotedRoom.DoesNotExist:   
            return NotFoundResponse(message="AllotedRoom not found")
        except Exception as e:
            return BadRequestResponse(data=str(e))
        return SuccessResponse(message="CHECKIN_COMPLETED")
    
    def get(self, request):
        try:
            checkin_requests = PreCheckin.objects.filter(status='checked_in').order_by('-created_at')
            serialized_requests = PreCheckinSerializer(checkin_requests, many=True).data
            return SuccessResponse(data=serialized_requests, message="Completed checkin requests")
        except Exception as e:
            return BadRequestResponse(data=str(e))
       

class RequestCheckin(APIView):
    permission_classes = []

    def post(self, request):
        try:
            user_name = request.data.get('user_name')
            phone_number = request.data.get('phone_number')
            property_id = request.data.get('property_id')

            if not user_name or not phone_number or not property_id:
                return BadRequestResponse(message="user_name, phone_number, and property_id are required")

            user, created = User.objects.get_or_create(phone=phone_number, defaults={'name': user_name})
            if created:
                user.set_password(get_random_string(length=8))
                user.save()

            property_instance = Property.objects.get(id=property_id)

            checkin_request = CheckinRequest.objects.create(user=user, property=property_instance)

            return SuccessResponse(data={"checkin_request_id": checkin_request.id}, message="Checkin request created successfully")
        except Property.DoesNotExist:
            return NotFoundResponse(message="Property not found")
        except Exception as e:
            return BadRequestResponse(data=str(e))


class CheckoutView(APIView):
    permission_classes = [PartnerPermission]

    def post(self, request):
        try:
            with transaction.atomic():
                room_id = request.data.get('room_id')
                _property = request.property
                room = Room.objects.filter(pk=room_id, property=_property).first()
                if not room:
                    return NotFoundResponse(message="Room not found")
                if not room.checked_in:
                    return BadRequestResponse(message="Room not checked in")
                room.checked_in = False
                room.save()
                guests = Guest.objects.filter(room=room, checked_out=False)
                for guest in guests:
                    guest.checked_in = False
                    guest.checked_out = True
                    guest.check_out_date = datetime.now()
                    guest.save()

                    # Send notifications
                    send_notification.delay(guest.user.id, NotificationCategory.USER_CHECKOUT.name, {
                        'username': guest.user.name,
                        'property_name': room.property.name
                    })
                    msg = Messages(guest.user.phone.national_number)
                    msg.send_bulk_sms(MessageTemplates.CHECKOUT.value, f'{room.room_no}, {room.property.name}')
                return SuccessResponse(message="Checked out successfully")
        except Exception as e:
            return BadRequestResponse(data=str(e))
