from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from stay.models import Property
from stay.serializers import PropertySerializer, PropertyRetrieveSerializer, RoomSerializer, PropertyListSerializer, PropertyAddressSerializer
from nestafar.responses import SuccessResponse, BadRequestResponse, CreateResponse
from core.permissions import PartnerPermission, RolePermission, ReadOnlyViewPermission
from django.db import transaction
from rest_framework.views import APIView
from stay.models import StayAmenities, RoomAmenities
from rest_framework.decorators import action
from service.subapps.food.models import FoodOrder
from service.subapps.food.serializers import FoodOrderSerializer
from geo.models import AdministrationArea
import math
from django.db.models import Q

class PropertyModelViewSet(viewsets.ModelViewSet):
    queryset = Property.objects.all()
    serializer_class = PropertyListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Property.objects.filter(staffs__user=self.request.user)
        
        try:
            body = self.request.data
            latitude = body.get('latitude')
            longitude = body.get('longitude')
            if latitude is not None and longitude is not None:
                latitude = float(latitude)
                longitude = float(longitude)
                distance_filter = self.get_distance_filter(latitude, longitude)
                queryset = queryset.filter(distance_filter)
        except ValueError:
            raise BadRequestResponse(message="Invalid latitude or longitude") from None

        return queryset

    def get_distance_filter(self, latitude, longitude):
        radius = 500  # in meters
        radius_in_degrees = radius / 111320

        min_lat = latitude - radius_in_degrees
        max_lat = latitude + radius_in_degrees
        min_lon = longitude - radius_in_degrees / math.cos(math.radians(latitude))
        max_lon = longitude + radius_in_degrees / math.cos(math.radians(latitude))

        bbox_filter = Q(
            location__latitude__range=(min_lat, max_lat),
            location__longitude__range=(min_lon, max_lon)
        )

        return bbox_filter

    def create(self, request, *args, **kwargs):
        serializer = PropertySerializer(data=request.data)
        image = request.FILES.get('image')
        if not serializer.is_valid():
            return BadRequestResponse(data=serializer.errors)

        partner_profile = request.user.partner_profile
        if partner_profile.max_rooms > 0:
            with transaction.atomic():
                property_instance = Property(**serializer.validated_data)
                property_instance.save()
                if image:
                    property_instance.photo.save(property_instance.name + '/' + image.name, image.file)
                    property_instance.save()
                property_instance.staffs.add(partner_profile)
                property_instance.save()

                partner_profile.active_property = property_instance
                partner_profile.save()

            result = serializer.data
            result['id'] = property_instance.id
            return CreateResponse(data=result)
        else:
            return BadRequestResponse(message="User not allowed to add a property")

    def update(self, request, pk=None, *args, **kwargs):
        image_file = request.FILES.get('image')
        instance = self.get_object()
        serializer = PropertySerializer(instance, data=request.data)

        if not serializer.is_valid():
            return BadRequestResponse(data=serializer.errors)
        
        with transaction.atomic():
            serializer.save()
            if image_file:
                instance.photo.save(instance.name + '/' + image_file.name, image_file.file)
                instance.save()

        return SuccessResponse(data=serializer.data)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = PropertyRetrieveSerializer(instance)
        return SuccessResponse(data=serializer.data)

    @action(methods=['PUT'], detail=True, url_path='amenities', url_name='amenities')
    def amenities(self, request):
        instance = self.get_object()
        amenities = request.data.get('amenities')
        if not amenities:
            return BadRequestResponse(data={}, message="Amenities data missing")

        success = StayAmenities.validate_amenities(amenities)
        if not success:
            return BadRequestResponse(data={}, message="Invalid amenities")
        
        with transaction.atomic():
            instance = Property.objects.select_for_update().get(pk=instance.pk)
            instance.amenities = amenities
            instance.save()
        return SuccessResponse(message="Amenities updated successfully")

    @action(methods=['GET'], detail=True, url_path='running_bill', url_name='running_bill')
    def running_bill(self, request, pk=None):
        property_instance = self.get_object()
        property_bill = []
        for room in property_instance.property_rooms.all():
            room_serializer = RoomSerializer(room)
            orders = FoodOrder.objects.filter(guest__room=room)
            orders = FoodOrderSerializer(orders, many=True)
            total_bill = 0
            for order in orders.data:
                total_bill += order.get('total')
            data = {
                'total_bill': total_bill,
                'orders': orders.data,
                'room': room_serializer.data
            }
            property_bill.append(data)
        return SuccessResponse(data=property_bill, message="Running Bill")

    @action(methods=['POST'], detail=True, url_path='address', url_name='address')
    def address(self, request, pk=None):
        property_instance = self.get_object()
        serializer = PropertyAddressSerializer(data=request.data)
        if not serializer.is_valid():
            return BadRequestResponse(data=serializer.errors)
        
        administrative_area = AdministrationArea.objects.filter(pincode=serializer.validated_data['pincode']).first()
        if not administrative_area:
            return BadRequestResponse(message="Invalid pincode")

        location = property_instance.location
        location.address = serializer.validated_data['address']
        location.administrative_area = administrative_area
        location.save()
        return SuccessResponse(message="Address updated successfully")

class StayAmenitiesView(APIView):
    permission_classes = [IsAuthenticated, ReadOnlyViewPermission]

    def get(self, request):
        amenities = StayAmenities.list_amenities()
        return SuccessResponse(data=amenities)


class RoomAmenitiesView(APIView):
    permission_classes = [IsAuthenticated, ReadOnlyViewPermission]

    def get(self, request):
        amenities = RoomAmenities.list_amenities()
        return SuccessResponse(data=amenities)
