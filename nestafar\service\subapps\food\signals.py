from django.db.models.signals import post_save, pre_save, pre_delete
from django.dispatch import receiver
from .models import FoodService
from .tasks import setup_foodservice_schedule
from django_celery_beat.models import PeriodicTask, CrontabSchedule

@receiver(pre_save, sender=FoodService)
def delete_existing_schedules(sender, instance, **kwargs):
    if instance.pk:
        try:
            # Get existing schedules for the instance
            show_task_name = f'show_service_{instance.pk}'
            hide_task_name = f'hide_service_{instance.pk}'
            
            show_task = PeriodicTask.objects.get(name=show_task_name)
            hide_task = PeriodicTask.objects.get(name=hide_task_name)
            
            # Delete existing schedules
            show_task.crontab.delete()
            hide_task.crontab.delete()
            
            show_task.delete()
            hide_task.delete()
            
        except PeriodicTask.DoesNotExist:
            pass

@receiver(post_save, sender=FoodService)
def create_or_update_foodservice_schedule(sender, instance, created, **kwargs):
    # Setup new schedules
    setup_foodservice_schedule(instance)

@receiver(pre_delete, sender=FoodService)
def delete_foodservice_schedules(sender, instance, **kwargs):
    try:
        # Get existing schedules for the instance
        show_task_name = f'show_service_{instance.pk}'
        hide_task_name = f'hide_service_{instance.pk}'
        
        show_task = PeriodicTask.objects.get(name=show_task_name)
        hide_task = PeriodicTask.objects.get(name=hide_task_name)
        
        # Delete existing schedules
        show_task.crontab.delete()
        hide_task.crontab.delete()
        
        show_task.delete()
        hide_task.delete()
        
    except PeriodicTask.DoesNotExist:
        pass
