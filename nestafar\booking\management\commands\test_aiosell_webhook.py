"""
Django management command to test AIOSell webhook endpoint.
Usage: python manage.py test_aiosell_webhook [options]
"""

import json
import requests
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from stay.models import Property
from booking.models import Reservation


class Command(BaseCommand):
    help = 'Test the AIOSell webhook endpoint with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--url',
            type=str,
            default='http://localhost:8000',
            help='Base URL of the server (default: http://localhost:8000)'
        )
        parser.add_argument(
            '--hotel-code',
            type=str,
            help='Hotel code to use for testing (if not provided, will try to find one)'
        )
        parser.add_argument(
            '--skip-error-tests',
            action='store_true',
            help='Skip error case testing'
        )
        parser.add_argument(
            '--booking-id',
            type=str,
            default='TEST-111222333',
            help='Booking ID to use for testing (default: TEST-111222333)'
        )
        parser.add_argument(
            '--timeout',
            type=int,
            default=30,
            help='Request timeout in seconds (default: 30)'
        )

    def handle(self, *args, **options):
        self.base_url = options['url']
        self.webhook_url = f"{self.base_url}/booking/webhook/aiosell/"
        self.timeout = options['timeout']
        
        self.stdout.write(
            self.style.HTTP_INFO("AIOSell Webhook Test Management Command")
        )
        self.stdout.write("=" * 50)
        
        # Get or validate hotel code
        hotel_code = self.get_hotel_code(options.get('hotel_code'))
        if not hotel_code:
            raise CommandError(
                "No hotel code available for testing. "
                "Use --hotel-code option or ensure at least one property has a hotel_code set."
            )
        
        booking_id = options['booking_id']
        
        # Clean up any existing test reservation
        self.cleanup_test_reservation(booking_id)
        
        self.stdout.write(f"\nUsing hotel code: {hotel_code}")
        self.stdout.write(f"Using booking ID: {booking_id}")
        self.stdout.write(f"Testing endpoint: {self.webhook_url}")
        
        # Run main tests
        success = self.test_webhook_operations(hotel_code, booking_id)
        
        # Run error tests unless skipped
        if not options['skip_error_tests']:
            self.test_error_cases()
        
        # Final summary
        self.stdout.write("\n" + "=" * 50)
        if success:
            self.stdout.write(
                self.style.SUCCESS("✅ All webhook tests completed successfully!")
            )
        else:
            self.stdout.write(
                self.style.ERROR("❌ Some webhook tests failed. Check the output above.")
            )
        self.stdout.write("=" * 50)

    def get_hotel_code(self, provided_code):
        """Get hotel code for testing"""
        if provided_code:
            # Validate provided hotel code
            if Property.objects.filter(hotel_code=provided_code).exists():
                return provided_code
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"Hotel code '{provided_code}' not found in database. "
                        "Will try to find an existing one..."
                    )
                )
        
        # Try to find any property with a hotel code
        property_with_code = Property.objects.exclude(
            hotel_code__isnull=True
        ).exclude(hotel_code='').first()
        
        if property_with_code:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Found property '{property_with_code.name}' with hotel code: {property_with_code.hotel_code}"
                )
            )
            return property_with_code.hotel_code
        
        return None

    def cleanup_test_reservation(self, booking_id):
        """Clean up any existing test reservation"""
        try:
            existing = Reservation.objects.filter(external_booking_id=booking_id)
            if existing.exists():
                count = existing.count()
                existing.delete()
                self.stdout.write(
                    self.style.WARNING(f"Cleaned up {count} existing test reservation(s)")
                )
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Could not clean up existing reservations: {str(e)}")
            )

    def create_sample_booking_data(self, hotel_code, booking_id):
        """Create sample booking data for testing"""
        today = datetime.now().date()
        checkin_date = today + timedelta(days=2)
        checkout_date = checkin_date + timedelta(days=2)

        return {
            "action": "book",
            "hotelCode": hotel_code,
            "bookingId": booking_id,
            "cmBookingId": f"CM-{booking_id}",
            "bookedOn": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "checkin": checkin_date.strftime("%Y-%m-%d"),
            "checkout": checkout_date.strftime("%Y-%m-%d"),
            "channel": "DIRECT",
            "segment": "OTA",
            "specialRequests": "Airport Taxi Required",
            "pah": False,
            "amount": {
                "amountAfterTax": 1204.0,
                "amountBeforeTax": 1075.0,
                "tax": 129.0,
                "currency": "INR"
            },
            "guest": {
                "firstName": "John",
                "lastName": "Doe",
                "email": "<EMAIL>",
                "phone": "9988776655",
                "address": {
                    "line1": "123 Test Street",
                    "city": "Bangalore",
                    "state": "Karnataka",
                    "country": "India",
                    "zipCode": "560035"
                }
            },
            "rooms": [
                {
                    "roomCode": "DELUXE",
                    "rateplanCode": "DELUXE-S-101",
                    "guestName": "John Doe",
                    "occupancy": {
                        "adults": 2,
                        "children": 1
                    },
                    "prices": [
                        {
                            "date": checkin_date.strftime("%Y-%m-%d"),
                            "sellRate": 602.0
                        },
                        {
                            "date": (checkin_date + timedelta(days=1)).strftime("%Y-%m-%d"),
                            "sellRate": 602.0
                        }
                    ]
                }
            ]
        }

    def test_webhook_operations(self, hotel_code, booking_id):
        """Test main webhook operations: book, modify, cancel"""
        sample_booking_data = self.create_sample_booking_data(hotel_code, booking_id)
        
        # Test 1: Booking creation
        self.stdout.write(self.style.HTTP_INFO("\n1. Testing booking creation..."))
        success = self.test_booking_creation(sample_booking_data)
        
        if not success:
            return False
        
        # Test 2: Booking modification
        self.stdout.write(self.style.HTTP_INFO("\n2. Testing booking modification..."))
        success = self.test_booking_modification(sample_booking_data)
        
        if not success:
            return False
        
        # Test 3: Booking cancellation
        self.stdout.write(self.style.HTTP_INFO("\n3. Testing booking cancellation..."))
        success = self.test_booking_cancellation(sample_booking_data)
        
        return success

    def test_booking_creation(self, sample_booking_data):
        """Test booking creation"""
        try:
            # Test invalid action
            invalid_action_data = {
                "action": "invalid_action",
                "hotelCode": "TEST-HOTEL",
                "bookingId": "TEST-123"
            }
            
            response = requests.post(
                self.webhook_url,                
                json=invalid_action_data,
                timeout=self.timeout
            )                
            self.stdout.write(f"Status Code: {response.status_code}")
            try:
                response_data = response.json()
                self.stdout.write(f"Response: {response_data}")
            except json.JSONDecodeError:
                self.stdout.write(f"Response (raw): {response.text}")              
            
            if response.status_code == 400:
                self.stdout.write(self.style.SUCCESS("✅ Invalid action validation PASSED"))
            else:
                self.stdout.write(self.style.ERROR("❌ Invalid action validation FAILED"))
            
            # Test booking creation with valid data
            response = requests.post(
                self.webhook_url,
                json=sample_booking_data,
                headers={'Content-Type': 'application/json'},
                timeout=self.timeout
            )
            
            self.stdout.write(f"Status Code: {response.status_code}")
            self.stdout.write(f"Response: {response.json()}")
            
            if response.status_code == 201:
                self.stdout.write(self.style.SUCCESS("✅ Booking creation test PASSED"))
                return True
            else:
                self.stdout.write(self.style.ERROR("❌ Booking creation test FAILED"))
                return False
                
        except requests.exceptions.ConnectionError:
            self.stdout.write(
                self.style.ERROR(
                    "❌ Connection failed. Make sure your Django server is running on the specified URL"
                )
            )
            return False
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed with error: {str(e)}"))
            return False

    def test_booking_modification(self, sample_booking_data):
        """Test booking modification"""
        try:
            modify_data = sample_booking_data.copy()
            modify_data['action'] = 'modify'
            new_checkin = (datetime.strptime(modify_data['checkin'], '%Y-%m-%d').date() + timedelta(days=1))
            modify_data['checkin'] = new_checkin.strftime('%Y-%m-%d')
            modify_data['specialRequests'] = 'Late check-in required'

            # Update room prices to match new dates
            modify_data['rooms'][0]['prices'] = [
                {"date": new_checkin.strftime('%Y-%m-%d'), "sellRate": 602.0},
                {"date": (new_checkin + timedelta(days=1)).strftime('%Y-%m-%d'), "sellRate": 602.0}
            ]
            
            response = requests.post(
                self.webhook_url,
                json=modify_data,
                headers={'Content-Type': 'application/json'},
                timeout=self.timeout
            )
            
            self.stdout.write(f"Status Code: {response.status_code}")
            self.stdout.write(f"Response: {response.json()}")
            
            if response.status_code == 200:
                self.stdout.write(self.style.SUCCESS("✅ Booking modification test PASSED"))
                return True
            else:
                self.stdout.write(self.style.ERROR("❌ Booking modification test FAILED"))
                return False
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Modification test failed: {str(e)}"))
            return False

    def test_booking_cancellation(self, sample_booking_data):
        """Test booking cancellation"""
        try:
            cancel_data = {
                "action": "cancel",
                "hotelCode": sample_booking_data['hotelCode'],
                "channel": sample_booking_data['channel'],
                "bookingId": sample_booking_data['bookingId']
            }
            
            response = requests.post(
                self.webhook_url,
                json=cancel_data,
                headers={'Content-Type': 'application/json'},
                timeout=self.timeout
            )
            
            self.stdout.write(f"Status Code: {response.status_code}")
            self.stdout.write(f"Response: {response.json()}")
            
            if response.status_code == 200:
                self.stdout.write(self.style.SUCCESS("✅ Booking cancellation test PASSED"))
                return True
            else:
                self.stdout.write(self.style.ERROR("❌ Booking cancellation test FAILED"))
                return False
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Cancellation test failed: {str(e)}"))
            return False

    def test_error_cases(self):
        """Test error handling scenarios"""
        self.stdout.write(self.style.HTTP_INFO("\n" + "=" * 50))
        self.stdout.write(self.style.HTTP_INFO("Testing error cases..."))
        self.stdout.write("=" * 50)
        
        # Test 1: Missing required fields
        self.stdout.write(self.style.HTTP_INFO("\nTest 1: Missing required fields"))
        invalid_data = {"action": "book"}  # Missing hotelCode and bookingId
        
        try:
            response = requests.post(
                self.webhook_url, 
                json=invalid_data, 
                timeout=self.timeout
            )
            self.stdout.write(f"Status Code: {response.status_code}")
            self.stdout.write(f"Response: {response.json()}")
            
            if response.status_code == 400:
                self.stdout.write(self.style.SUCCESS("✅ Missing fields validation PASSED"))
            else:
                self.stdout.write(self.style.ERROR("❌ Missing fields validation FAILED"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed: {str(e)}"))
        
        # Test 2: Invalid action
        self.stdout.write(self.style.HTTP_INFO("\nTest 2: Invalid action"))
        invalid_action_data = {
            "action": "invalid_action",
            "hotelCode": "TEST-HOTEL",
            "bookingId": "TEST-123"
        }
        
        try:
            response = requests.post(
                self.webhook_url,
                json=invalid_action_data,
                timeout=self.timeout
            )           
            self.stdout.write(f"Status Code: {response.status_code}")
            self.stdout.write(f"Response: {response.json()}")
            
            if response.status_code == 400:
                self.stdout.write(self.style.SUCCESS("✅ Invalid action validation PASSED"))
            else:
                self.stdout.write(self.style.ERROR("❌ Invalid action validation FAILED"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed: {str(e)}"))
        
        # Test 3: Property not found
        self.stdout.write(self.style.HTTP_INFO("\nTest 3: Property not found"))
        no_property_data = {
            "action": "book",
            "hotelCode": "NONEXISTENT-HOTEL",
            "bookingId": "TEST-456",
            "checkin": "2024-12-10",
            "checkout": "2024-12-12",
            "guest": {
                "firstName": "Test",
                "lastName": "User",
                "phone": "1234567890"
            },
            "rooms": [{"occupancy": {"adults": 1, "children": 0}}],
            "amount": {"amountAfterTax": 1000}
        }
        
        try:
            response = requests.post(self.webhook_url, json=no_property_data)
            self.stdout.write(f"Status Code: {response.status_code}")
            self.stdout.write(f"Response: {response.json()}")
            
            if response.status_code == 404:
                self.stdout.write(self.style.SUCCESS("✅ Property not found validation PASSED"))
            else:
                self.stdout.write(self.style.ERROR("❌ Property not found validation FAILED"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed: {str(e)}"))
