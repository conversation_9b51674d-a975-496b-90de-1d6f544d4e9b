import os
from celery import Celery
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "nestafar.settings")

app = Celery("nestafar")

app.conf.enable_utc = False

app.config_from_object('django.conf:settings', namespace="CELERY")

app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

app.conf.beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'

app.conf.update(timezone='Asia/Kolkata')

@app.task(bind=True)
def debug_task(self):
    print(f"Request: {self.request!r}")