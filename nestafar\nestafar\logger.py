import logging
import os
import uuid
import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pythonjsonlogger import jsonlogger
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings

# Ensure the logs directory exists
LOGS_DIR = os.path.join(settings.BASE_DIR, 'logs')
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)  # Create the logs directory if it doesn't exist

# Use consistent log file names
class Logger:
    formatter = jsonlogger.JsonFormatter()

    __metric_logger_file_name = os.path.join(LOGS_DIR, "metric.log")
    __audit_logger_file_name = os.path.join(LOGS_DIR, "audit.log")
    __error_logger_file_name = os.path.join(LOGS_DIR, "error.log")
    __celery_logger_file_name = os.path.join(LOGS_DIR, "celery.log")

    def __init__(self):
        self.__stream_handler = logging.StreamHandler()

        self.__metrics_logger = logging.getLogger('metrics')
        self.__metrics_logger.setLevel(logging.INFO)
        self.__metrics_logger.propagate = False

        self.__audit_logger = logging.getLogger('audit')
        self.__audit_logger.setLevel(logging.INFO)
        self.__audit_logger.propagate = False

        self.__error_logger = logging.getLogger('error')
        self.__error_logger.setLevel(logging.ERROR)
        self.__error_logger.propagate = False

        self.__celery_logger = logging.getLogger('celery')
        self.__celery_logger.setLevel(logging.INFO)
        self.__celery_logger.propagate = False

        self.init_metric_logger(self.formatter)
        self.init_audit_logger(self.formatter)
        self.init_error_logger(self.formatter)
        self.init_celery_logger(self.formatter)

    def init_metric_logger(self, formatter):
        self.__metrics_file_handler = RotatingFileHandler(self.__metric_logger_file_name, maxBytes=10*1024*1024, backupCount=5)
        self.__metrics_file_handler.setFormatter(formatter)
        self.__metrics_file_handler.setLevel(logging.INFO)
        self.__metrics_logger.addHandler(self.__metrics_file_handler)
        self.__metrics_logger.info('Metric logger initialized')

    def init_audit_logger(self, formatter):
        self.__audit_file_handler = RotatingFileHandler(self.__audit_logger_file_name, maxBytes=10*1024*1024, backupCount=5)
        self.__audit_file_handler.setFormatter(formatter)
        self.__audit_file_handler.setLevel(logging.INFO)
        self.__audit_logger.addHandler(self.__audit_file_handler)
        self.__audit_logger.info('Audit logger initialized')

    def init_error_logger(self, formatter):
        self.__error_file_handler = RotatingFileHandler(self.__error_logger_file_name, maxBytes=10*1024*1024, backupCount=5)
        self.__error_file_handler.setFormatter(formatter)
        self.__error_file_handler.setLevel(logging.ERROR)
        self.__error_logger.addHandler(self.__error_file_handler)
        self.__error_logger.info('Error logger initialized')

    def init_celery_logger(self, formatter):
        self.__celery_file_handler = TimedRotatingFileHandler(self.__celery_logger_file_name, when='midnight', interval=1)
        self.__celery_file_handler.setFormatter(formatter)
        self.__celery_file_handler.setLevel(logging.INFO)
        self.__celery_logger.addHandler(self.__celery_file_handler)
        self.__celery_logger.info("Celery logger initialized")


class DjangoRequestLogger(MiddlewareMixin):
    metric_logger = logging.getLogger('metrics')
    audit_logger = logging.getLogger('audit')

    def process_request(self, request):
        curr_time = datetime.datetime.now()
        request.start_time = curr_time
        request._request_id = str(uuid.uuid4())
        self.audit_logger.info({'path': request.path_info, 'time': curr_time, 'request_id': request._request_id, 'data': self.get_request_information(request)})
        
    def process_response(self, request, response):
        curr_time = datetime.datetime.now()
        response_time = curr_time - request.start_time
        self.audit_logger.info({'path': request.path_info, 'time': curr_time, 'request_id': request._request_id, 'data': self.get_response_information(response)})
        self.metric_logger.info({'path': request.path_info, 'time': response_time})
        return response
    
    def process_exception(self, request, exception):
        curr_time = datetime.datetime.now()
        self.audit_logger.error({'path': request.path_info, 'time': curr_time, 'request_id': request._request_id, 'data': self.get_request_information(request), 'exception': str(exception)})
        return None
    
    def get_request_information(self, request):
        request_information = dict()
        request_information['path'] = request.path_info
        request_information['headers'] = request.headers
        request_information['cookies'] = request.method
        request_information['method'] = request.method
        return request_information
    
    def get_response_information(self, response):
        response_information = dict()
        response_information['status_code'] = response.status_code
        response_information['headers'] = response.headers
        return response_information
